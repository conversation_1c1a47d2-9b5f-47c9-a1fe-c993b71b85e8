from typing import List, Dict, Any
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.proposals_format_queue_controller import ProposalsFormatQueueController
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
import json
from services.proposal.proposal_decoding_service import ProposalDecodingService

from cryptography.exceptions import InvalidKey, InvalidTag
from cryptography.hazmat.primitives.asymmetric.padding import OAEP, MGF1
from cryptography.hazmat.primitives import hashes

class ProposalVolumeRetrievalService:
    """Service for retrieving and decrypting proposal volumes from review and format queues."""
    
    def __init__(self):
        self.proposal_decoding_service = ProposalDecodingService()
    
    async def get_all_volumes_from_review(
        self,
        db: AsyncSession,
        tenant_id: str,
        opportunity_id: str
    ) -> List[List[Dict[str, Any]] | None]:
        """
        Retrieve all volumes from ProposalsInReview for a given tenant_id and opportunity_id.
        Returns a list of volumes, where each volume is a list of dictionaries or None if empty.
        """
        logger.info(f"get_all_volumes_from_review called with tenant_id={tenant_id}, opportunity_id={opportunity_id}")
        
        try:
            # Query proposals in review by opportunity ID
            proposals = await ProposalsInReviewController.get_by_opps_id(db, opportunity_id)
            
            if not proposals:
                logger.info(f"No proposals found for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return []

            # Get the maximum version to ensure we fetch the latest data
            current_version = await ProposalsInReviewController.get_current_version(db, tenant_id, opportunity_id)
            if current_version is None:
                logger.error(f"Could not determine current version for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return []

            # Filter proposals by the latest version
            latest_proposals = [p for p in proposals if p.version == current_version]
            
            # Initialize the result list with None for each volume
            max_volume_number = max((p.volume_number for p in latest_proposals), default=0)
            all_volumes: List[List[Dict[str, Any]] | None] = [None] * max_volume_number

            try:
                key_info = await self.proposal_decoding_service.get_key_pair(db, tenant_id)
                logger.debug(f"Key info retrieved for tenant_id={tenant_id}: {key_info is not None}")
                
                if not key_info:
                    logger.error(f"No encryption key info found for tenant_id={tenant_id}")
                    return [None] * max_volume_number
                    
                if not key_info.get("passphrase"):
                    logger.error(f"No passphrase found in key info for tenant_id={tenant_id}")
                    return [None] * max_volume_number

                passphrase = key_info["passphrase"]
                logger.debug(f"Passphrase retrieved for tenant_id={tenant_id}, length: {len(passphrase) if passphrase else 0}")
                
            except Exception as e:
                logger.error(f"Error retrieving key pair for tenant_id={tenant_id}: {str(e)}")
                return [None] * max_volume_number

            # Enhanced passphrase verification with detailed logging
            # try:
            #     print(f"Passfrase: {passphrase}")
            #     passphrase_valid = await self.proposal_decoding_service.verify_passphrase(db, tenant_id, passphrase)
            #     logger.debug(f"Passphrase verification result for tenant_id={tenant_id}: {passphrase_valid}")
                
            #     if not passphrase_valid:
            #         logger.error(f"Invalid passphrase for tenant_id={tenant_id}")
            #         # Additional debugging: Check if encryption record exists
            #         from services.proposal.proposal_encryption_service import ProposalEncryptionService
            #         encryption_exists = await ProposalEncryptionService.exists_by_tenant_id(db, tenant_id)
            #         logger.debug(f"Encryption record exists for tenant_id={tenant_id}: {encryption_exists}")
                    
            #         if not encryption_exists:
            #             logger.error(f"No encryption record found for tenant_id={tenant_id}")
                    
            #         return [None] * max_volume_number
                    
            # except Exception as e:
            #     logger.error(f"Error verifying passphrase for tenant_id={tenant_id}: {str(e)}")
            #     return [None] * max_volume_number

            # Organize proposals by volume number
            for proposal in latest_proposals:
                volume_index = proposal.volume_number - 1  # Convert to 0-based index                    # Decrypt the proposal_data with enhanced error handling
                try:
                    decrypted_data = await self.proposal_decoding_service.decrypt_section(
                        db, tenant_id, proposal.proposal_data, passphrase
                    )
                    logger.debug(f"Successfully decrypted data for volume {proposal.volume_number}, size: {len(decrypted_data)} bytes")
                    
                except (InvalidKey, InvalidTag) as decrypt_error:
                    logger.error(f"Cryptographic decryption failed for volume {proposal.volume_number}: {str(decrypt_error)}")
                    all_volumes[volume_index] = None
                    continue
                except Exception as decrypt_error:
                    logger.error(f"Unexpected decryption error for volume {proposal.volume_number}: {str(decrypt_error)}")
                    all_volumes[volume_index] = None
                    continue
                
                # Decode the JSON data
                try:
                    volume_data = json.loads(decrypted_data.decode('utf-8'))
                    all_volumes[volume_index] = volume_data
                    logger.debug(f"Successfully parsed JSON for volume {proposal.volume_number}")
                    
                except json.JSONDecodeError as json_error:
                    logger.error(f"JSON decoding failed for volume {proposal.volume_number}: {str(json_error)}")
                    logger.debug(f"Problematic data preview: {decrypted_data[:100]}...")
                    all_volumes[volume_index] = None
                except UnicodeDecodeError as unicode_error:
                    logger.error(f"Unicode decoding failed for volume {proposal.volume_number}: {str(unicode_error)}")
                    all_volumes[volume_index] = None
                        
                except Exception as e:
                    logger.error(f"Unexpected error processing volume {proposal.volume_number}: {str(e)}")
                    all_volumes[volume_index] = None

            successful_volumes = sum(1 for vol in all_volumes if vol is not None)
            logger.info(f"Retrieved {successful_volumes}/{max_volume_number} volumes successfully for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
            return all_volumes

        except Exception as e:
            logger.error(f"Critical error retrieving volumes for tenant_id={tenant_id}, opportunity_id={opportunity_id}: {str(e)}", exc_info=True)
            return []

    async def get_all_volumes_from_format(
        self,
        db: AsyncSession,
        tenant_id: str,
        opportunity_id: str
    ) -> List[List[Dict[str, Any]] | None]:
        """
        Retrieve all volumes from ProposalsFormatQueue for a given tenant_id and opportunity_id.
        Returns a list of volumes, where each volume is a list of dictionaries or None if empty.
        """
        logger.info(f"get_all_volumes_from_format called with tenant_id={tenant_id}, opportunity_id={opportunity_id}")
        
        try:
            # Query proposals in format queue by opportunity ID
            proposals = await ProposalsFormatQueueController.get_by_opps_id(db, opportunity_id)
            
            if not proposals:
                logger.info(f"No proposals found in format queue for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return []

            # Get the maximum version to ensure we fetch the latest data
            current_version = await ProposalsFormatQueueController.get_current_version(db, tenant_id, opportunity_id)
            if current_version is None:
                logger.error(f"Could not determine current version for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return []

            # Filter proposals by the latest version
            latest_proposals = [p for p in proposals if p.version == current_version]
            
            if not latest_proposals:
                logger.info(f"No proposals found for version {current_version}")
                return []

            # Process the latest proposal record
            proposal = latest_proposals[0]
            try:
                # Enhanced encryption details retrieval
                key_info = await self.proposal_decoding_service.get_key_pair(db, tenant_id)
                if not key_info or not key_info.get("passphrase"):
                    logger.error(f"No encryption key info or passphrase found for tenant_id={tenant_id}")
                    return [None]

                passphrase = key_info["passphrase"]
                
                # Enhanced passphrase verification
                passphrase_valid = await self.proposal_decoding_service.verify_passphrase(db, tenant_id, passphrase)
                if not passphrase_valid:
                    logger.error(f"Invalid passphrase for tenant_id={tenant_id}")
                    return [None]

                # Validate encrypted data length
                key_size_bytes = 4096 // 8  # RSA key size in bytes
                min_data_length = 16 + key_size_bytes  # IV (16 bytes) + encrypted AES key
                if len(proposal.proposal_data) < min_data_length:
                    logger.error(f"Invalid proposal_data length for opportunity_id={opportunity_id}: {len(proposal.proposal_data)} bytes (minimum required: {min_data_length})")
                    return [None]

                # Decrypt the proposal_data
                decrypted_data = await self.proposal_decoding_service.decrypt_section(
                    db, tenant_id, proposal.proposal_data, passphrase
                )
                
                # Decode the JSON data
                volume_data = json.loads(decrypted_data.decode('utf-8'))
                
                all_volumes: List[List[Dict[str, Any]] | None] = [volume_data]
                
                logger.info(f"Successfully retrieved and decrypted volumes from format queue for tenant_id={tenant_id}, opportunity_id={opportunity_id}")
                return all_volumes
                
            except (ValueError, InvalidKey, InvalidTag) as e:
                logger.error(f"Decryption failed for opportunity_id={opportunity_id}: {str(e)}")
                return [None]
            except json.JSONDecodeError as e:
                logger.error(f"JSON decoding failed for opportunity_id={opportunity_id}: {str(e)}")
                return [None]
            except Exception as e:
                logger.error(f"Unexpected error decoding proposal data for opportunity_id={opportunity_id}: {str(e)}")
                return [None]

        except Exception as e:
            logger.error(f"Error retrieving volumes from format queue for tenant_id={tenant_id}, opportunity_id={opportunity_id}: {str(e)}", exc_info=True)
            return []
        