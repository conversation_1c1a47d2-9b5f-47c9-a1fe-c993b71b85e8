"""
Enhanced Proposal Service
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from services.proposal.problem_analysis_framework import (
    ProblemAnalysisFramework, ProblemAnalysisResult
)
from services.proposal.solution_generation_engine import (
    SolutionGenerationEngine, SolutionGenerationResult
)
from services.proposal.problem_solution_validator import (
    ProblemSolutionValidator, ValidationReport
)
from services.proposal.enhanced_research_service import (
    EnhancedResearchService, ResearchType, ResearchDepth
)
from services.proposal.multi_agent.workflow import MultiAgentWorkflow

logger = logging.getLogger(__name__)


class EnhancedProposalService:
    """
    Comprehensive proposal generation service that integrates deep problem analysis,
    unique solution generation, and validation to create high-quality, targeted proposals.
    """
    
    def __init__(self):
        """Initialize the enhanced proposal service with all components."""
        self.problem_framework = ProblemAnalysisFramework()
        self.solution_engine = SolutionGenerationEngine()
        self.validator = ProblemSolutionValidator()
        self.research_service = EnhancedResearchService()
        self.multi_agent_workflow = MultiAgentWorkflow()
        
        logger.info("EnhancedProposalService: Initialized with comprehensive analysis capabilities")
    
    async def generate_enhanced_proposal_section(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        section_type: str,
        section_content: str,
        organization_name: str,
        opportunity_title: str,
        opportunity_description: str,
        enable_deep_analysis: bool = True,
        enable_solution_validation: bool = True,
        research_depth: ResearchDepth = ResearchDepth.DEEP
    ) -> Dict[str, Any]:
        """
        Generate enhanced proposal section with deep problem understanding and unique solutions.
        
        Args:
            opportunity_id: Unique identifier for the opportunity
            tenant_id: Tenant identifier
            source: Source of the opportunity (custom, sam, etc.)
            section_type: Type of section to generate
            section_content: Section requirements and content
            organization_name: Name of the target organization
            opportunity_title: Title of the opportunity
            opportunity_description: Description of the opportunity
            enable_deep_analysis: Whether to perform deep problem analysis
            enable_solution_validation: Whether to validate solutions
            research_depth: Depth of research to conduct
            
        Returns:
            Enhanced proposal section with analysis, solutions, and validation
        """
        logger.info(f"ENHANCED_PROPOSAL: Starting enhanced generation for {opportunity_id} - {section_type}")
        
        try:
            result = {
                'opportunity_id': opportunity_id,
                'section_type': section_type,
                'generation_timestamp': datetime.now().isoformat(),
                'enhanced_features_enabled': {
                    'deep_analysis': enable_deep_analysis,
                    'solution_validation': enable_solution_validation,
                    'research_depth': research_depth.value
                }
            }
            
            # Step 1: Conduct Enhanced Research (if enabled)
            if enable_deep_analysis:
                logger.info("ENHANCED_PROPOSAL: Conducting enhanced research")
                research_result = await self.research_service.conduct_comprehensive_research(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    organization_name=organization_name,
                    opportunity_title=opportunity_title,
                    opportunity_description=opportunity_description,
                    research_focus=[
                        ResearchType.PROBLEM_INVESTIGATION,
                        ResearchType.SOLUTION_RESEARCH,
                        ResearchType.INDUSTRY_ANALYSIS
                    ],
                    depth=research_depth
                )
                result['research_analysis'] = {
                    'insights_found': len(research_result.insights),
                    'key_findings': research_result.key_findings,
                    'confidence_score': research_result.confidence_score,
                    'data_sources': research_result.data_sources
                }
            
            # Step 2: Deep Problem Analysis (if enabled)
            problem_analysis = None
            if enable_deep_analysis:
                logger.info("ENHANCED_PROPOSAL: Conducting deep problem analysis")
                problem_analysis = await self.problem_framework.analyze_opportunity_problems(
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    organization_name=organization_name,
                    opportunity_title=opportunity_title,
                    opportunity_description=opportunity_description,
                    additional_context=section_content
                )
                
                result['problem_analysis'] = {
                    'problems_identified': len(problem_analysis.problems_identified),
                    'confidence_score': problem_analysis.confidence_score,
                    'key_findings': problem_analysis.key_findings[:3],
                    'problem_categories': [p.category.value for p in problem_analysis.problems_identified],
                    'severity_distribution': self._analyze_severity_distribution(problem_analysis.problems_identified)
                }
            
            # Step 3: Unique Solution Generation (if problem analysis available)
            solution_generation = None
            if problem_analysis:
                logger.info("ENHANCED_PROPOSAL: Generating unique solutions")
                solution_generation = await self.solution_engine.generate_unique_solutions(
                    problem_analysis=problem_analysis,
                    max_solutions=2
                )
                
                result['solution_generation'] = {
                    'solutions_generated': len(solution_generation.unique_solutions),
                    'innovation_score': solution_generation.innovation_score,
                    'feasibility_score': solution_generation.feasibility_score,
                    'alignment_score': solution_generation.alignment_score,
                    'overall_quality_score': solution_generation.overall_quality_score
                }
            
            # Step 4: Solution Validation (if enabled and solutions available)
            validation_report = None
            if enable_solution_validation and problem_analysis and solution_generation:
                logger.info("ENHANCED_PROPOSAL: Validating solutions")
                validation_report = await self.validator.validate_solutions(
                    problem_analysis=problem_analysis,
                    solution_generation=solution_generation
                )
                
                result['validation_report'] = {
                    'validation_result': validation_report.overall_result.value,
                    'overall_score': validation_report.overall_score,
                    'approved_solutions': len(validation_report.approved_solutions),
                    'rejected_solutions': len(validation_report.rejected_solutions),
                    'critical_issues': len([issue for issue in validation_report.validation_issues if issue.severity == "critical"])
                }
            
            # Step 5: Generate Enhanced Content using Multi-Agent System
            logger.info("ENHANCED_PROPOSAL: Generating enhanced content")
            
            # Prepare enhanced context for multi-agent system
            enhanced_context = {
                'problem_analysis': problem_analysis.to_dict() if problem_analysis else None,
                'solution_generation': solution_generation.to_dict() if solution_generation else None,
                'validation_report': validation_report.to_dict() if validation_report else None,
                'organization_name': organization_name,
                'opportunity_title': opportunity_title,
                'opportunity_description': opportunity_description
            }
            
            # Generate content using enhanced multi-agent workflow
            content_result = await self.multi_agent_workflow.generate_section_content(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                section_type=section_type,
                section_content=section_content,
                client_short_name=organization_name
            )
            
            # Enhance content result with analysis data
            if 'state' in content_result and hasattr(content_result['state'], 'add_context'):
                if problem_analysis:
                    content_result['state'].add_context('problem_analysis', problem_analysis.to_dict())
                if solution_generation:
                    content_result['state'].add_context('solution_generation', solution_generation.to_dict())
                if validation_report:
                    content_result['state'].add_context('validation_report', validation_report.to_dict())
            
            result['content_generation'] = content_result
            
            # Step 6: Calculate Overall Enhancement Score
            enhancement_score = self._calculate_enhancement_score(
                problem_analysis, solution_generation, validation_report
            )
            
            result['enhancement_metrics'] = {
                'overall_enhancement_score': enhancement_score,
                'problem_understanding_depth': problem_analysis.confidence_score if problem_analysis else 0.5,
                'solution_innovation_level': solution_generation.innovation_score if solution_generation else 0.5,
                'solution_validation_score': validation_report.overall_score if validation_report else 0.5,
                'content_quality_indicators': self._extract_quality_indicators(content_result)
            }
            
            logger.info(f"ENHANCED_PROPOSAL: Completed enhanced generation with score {enhancement_score:.2f}")
            return result
            
        except Exception as e:
            logger.error(f"ENHANCED_PROPOSAL: Enhanced generation failed: {e}")
            # Fallback to standard multi-agent generation
            logger.info("ENHANCED_PROPOSAL: Falling back to standard generation")
            return await self._fallback_generation(
                opportunity_id, tenant_id, section_type, section_content, organization_name
            )
    
    async def _fallback_generation(
        self,
        opportunity_id: str,
        tenant_id: str,
        section_type: str,
        section_content: str,
        organization_name: str
    ) -> Dict[str, Any]:
        """Fallback to standard multi-agent generation if enhanced features fail."""
        try:
            content_result = await self.multi_agent_workflow.generate_section_content(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                section_type=section_type,
                section_content=section_content,
                client_short_name=organization_name
            )
            
            return {
                'opportunity_id': opportunity_id,
                'section_type': section_type,
                'generation_timestamp': datetime.now().isoformat(),
                'enhanced_features_enabled': {
                    'deep_analysis': False,
                    'solution_validation': False,
                    'fallback_mode': True
                },
                'content_generation': content_result,
                'enhancement_metrics': {
                    'overall_enhancement_score': 0.5,
                    'fallback_reason': 'Enhanced features failed, using standard generation'
                }
            }
            
        except Exception as e:
            logger.error(f"ENHANCED_PROPOSAL: Fallback generation also failed: {e}")
            raise Exception(f"Both enhanced and fallback generation failed: {e}")
    
    def _analyze_severity_distribution(self, problems) -> Dict[str, int]:
        """Analyze the distribution of problem severities."""
        distribution = {}
        for problem in problems:
            severity = problem.severity.value
            distribution[severity] = distribution.get(severity, 0) + 1
        return distribution
    
    def _calculate_enhancement_score(
        self,
        problem_analysis: Optional[ProblemAnalysisResult],
        solution_generation: Optional[SolutionGenerationResult],
        validation_report: Optional[ValidationReport]
    ) -> float:
        """Calculate overall enhancement score based on all components."""
        score = 0.5  # Base score
        
        if problem_analysis:
            score += 0.2 * problem_analysis.confidence_score
        
        if solution_generation:
            score += 0.2 * solution_generation.overall_quality_score
        
        if validation_report:
            score += 0.1 * validation_report.overall_score
        
        return min(score, 1.0)
    
    def _extract_quality_indicators(self, content_result: Dict[str, Any]) -> List[str]:
        """Extract quality indicators from content generation result."""
        indicators = []
        
        if content_result.get('success', False):
            indicators.append("Content generation successful")
        
        if 'metadata' in content_result:
            metadata = content_result['metadata']
            if 'agent_results' in metadata:
                successful_agents = len([r for r in metadata['agent_results'] if r.get('success', False)])
                indicators.append(f"Successful agent executions: {successful_agents}")
        
        if 'final_content' in content_result and content_result['final_content']:
            content_length = len(content_result['final_content'])
            indicators.append(f"Content length: {content_length} characters")
        
        return indicators
