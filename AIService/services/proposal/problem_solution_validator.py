"""
Problem-Solution Validation System
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from datetime import datetime

from services.proposal.problem_analysis_framework import (
    ProblemAnalysisResult, ProblemStatement, SolutionRequirements
)
from services.proposal.solution_generation_engine import (
    SolutionGenerationResult, UniqueSolution, SolutionComponent
)
from llm_factory import get_llm

logger = logging.getLogger(__name__)


class ValidationResult(Enum):
    """Validation result types"""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"
    NEEDS_IMPROVEMENT = "needs_improvement"


class ValidationCategory(Enum):
    """Categories of validation"""
    PROBLEM_MAPPING = "problem_mapping"
    EFFECTIVENESS = "effectiveness"
    FEASIBILITY = "feasibility"
    STAKEHOLDER_ALIGNMENT = "stakeholder_alignment"
    RISK_ASSESSMENT = "risk_assessment"
    SUCCESS_CRITERIA = "success_criteria"


@dataclass
class ValidationIssue:
    """Individual validation issue"""
    issue_id: str
    category: ValidationCategory
    severity: str  # critical, high, medium, low
    title: str
    description: str
    affected_components: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class ValidationReport:
    """Comprehensive validation report"""
    validation_id: str
    opportunity_id: str
    tenant_id: str
    validation_timestamp: str
    overall_result: ValidationResult
    overall_score: float
    category_scores: Dict[str, float]
    validation_issues: List[ValidationIssue]
    recommendations: List[str]
    approved_solutions: List[str]  # Solution IDs that passed validation
    rejected_solutions: List[str]  # Solution IDs that failed validation
    improvement_suggestions: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class ProblemSolutionValidator:
    """
    Comprehensive validation system that ensures proposed solutions actually
    address identified problems and meet organizational needs and constraints.
    """
    
    def __init__(
        self,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        self.llm = get_llm(
            temperature=0.0,  # Very low temperature for objective validation
            num_ctx=8000,
            base_url=llm_api_url
        )
        
        logger.info("ProblemSolutionValidator: Initialized with objective validation capabilities")
    
    async def validate_solutions(
        self,
        problem_analysis: ProblemAnalysisResult,
        solution_generation: SolutionGenerationResult
    ) -> ValidationReport:
        """
        Perform comprehensive validation of solutions against identified problems.
        
        Args:
            problem_analysis: Comprehensive problem analysis result
            solution_generation: Generated solutions to validate
            
        Returns:
            ValidationReport with detailed validation results
        """
        logger.info(f"VALIDATION: Starting solution validation for {problem_analysis.opportunity_id}")
        
        try:
            validation_issues = []
            category_scores = {}
            approved_solutions = []
            rejected_solutions = []
            
            # Validate each solution
            for solution in solution_generation.unique_solutions:
                solution_issues = await self._validate_single_solution(
                    solution, problem_analysis, solution_generation
                )
                validation_issues.extend(solution_issues)
                
                # Determine if solution passes validation
                critical_issues = [issue for issue in solution_issues if issue.severity == "critical"]
                if not critical_issues:
                    approved_solutions.append(solution.solution_id)
                else:
                    rejected_solutions.append(solution.solution_id)
            
            # Calculate category scores
            category_scores = await self._calculate_category_scores(
                validation_issues, problem_analysis, solution_generation
            )
            
            # Calculate overall score
            overall_score = sum(category_scores.values()) / len(category_scores) if category_scores else 0.5
            
            # Determine overall result
            overall_result = self._determine_overall_result(overall_score, validation_issues)
            
            # Generate recommendations
            recommendations = await self._generate_validation_recommendations(
                validation_issues, problem_analysis, solution_generation
            )
            
            # Generate improvement suggestions
            improvement_suggestions = await self._generate_improvement_suggestions(
                validation_issues, rejected_solutions, solution_generation
            )
            
            # Create validation report
            report = ValidationReport(
                validation_id=f"VAL_{problem_analysis.opportunity_id}_{int(datetime.now().timestamp())}",
                opportunity_id=problem_analysis.opportunity_id,
                tenant_id=problem_analysis.tenant_id,
                validation_timestamp=datetime.now().isoformat(),
                overall_result=overall_result,
                overall_score=overall_score,
                category_scores=category_scores,
                validation_issues=validation_issues,
                recommendations=recommendations,
                approved_solutions=approved_solutions,
                rejected_solutions=rejected_solutions,
                improvement_suggestions=improvement_suggestions
            )
            
            logger.info(f"VALIDATION: Completed validation with score {overall_score:.2f}, {len(approved_solutions)} approved, {len(rejected_solutions)} rejected")
            return report
            
        except Exception as e:
            logger.error(f"VALIDATION: Solution validation failed: {e}")
            raise Exception(f"Solution validation failed: {e}")
    
    async def _validate_single_solution(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        solution_generation: SolutionGenerationResult
    ) -> List[ValidationIssue]:
        """Validate a single solution against all criteria."""
        logger.info(f"VALIDATION: Validating solution {solution.solution_id}")
        
        issues = []
        issue_counter = 1
        
        # 1. Problem Mapping Validation
        mapping_issues = await self._validate_problem_mapping(
            solution, problem_analysis, issue_counter
        )
        issues.extend(mapping_issues)
        issue_counter += len(mapping_issues)
        
        # 2. Effectiveness Validation
        effectiveness_issues = await self._validate_solution_effectiveness(
            solution, problem_analysis, issue_counter
        )
        issues.extend(effectiveness_issues)
        issue_counter += len(effectiveness_issues)
        
        # 3. Feasibility Validation
        feasibility_issues = await self._validate_solution_feasibility(
            solution, problem_analysis, issue_counter
        )
        issues.extend(feasibility_issues)
        issue_counter += len(feasibility_issues)
        
        # 4. Stakeholder Alignment Validation
        alignment_issues = await self._validate_stakeholder_alignment(
            solution, problem_analysis, issue_counter
        )
        issues.extend(alignment_issues)
        issue_counter += len(alignment_issues)
        
        # 5. Risk Assessment Validation
        risk_issues = await self._validate_risk_assessment(
            solution, problem_analysis, issue_counter
        )
        issues.extend(risk_issues)
        issue_counter += len(risk_issues)
        
        # 6. Success Criteria Validation
        criteria_issues = await self._validate_success_criteria(
            solution, problem_analysis, issue_counter
        )
        issues.extend(criteria_issues)
        
        logger.info(f"VALIDATION: Solution {solution.solution_id} has {len(issues)} validation issues")
        return issues
    
    async def _validate_problem_mapping(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        start_id: int
    ) -> List[ValidationIssue]:
        """Validate that solution components map to identified problems."""
        issues = []
        
        try:
            # Check if solution addresses all critical problems
            critical_problems = [p for p in problem_analysis.problems_identified 
                               if p.severity.value in ["critical", "high"]]
            
            addressed_problems = set(solution.target_problems)
            critical_problem_ids = set(p.problem_id for p in critical_problems)
            
            unaddressed_critical = critical_problem_ids - addressed_problems
            
            if unaddressed_critical:
                issues.append(ValidationIssue(
                    issue_id=f"MAP_{start_id:03d}",
                    category=ValidationCategory.PROBLEM_MAPPING,
                    severity="critical",
                    title="Critical Problems Not Addressed",
                    description=f"Solution does not address {len(unaddressed_critical)} critical problems: {list(unaddressed_critical)}",
                    affected_components=[comp.component_id for comp in solution.solution_components],
                    recommendations=[
                        "Add solution components to address all critical problems",
                        "Modify existing components to cover unaddressed problems",
                        "Provide justification for not addressing specific problems"
                    ]
                ))
            
            # Check component-problem mapping
            for component in solution.solution_components:
                if not component.addresses_problems:
                    issues.append(ValidationIssue(
                        issue_id=f"MAP_{start_id + len(issues) + 1:03d}",
                        category=ValidationCategory.PROBLEM_MAPPING,
                        severity="medium",
                        title="Component Without Problem Mapping",
                        description=f"Component {component.name} does not specify which problems it addresses",
                        affected_components=[component.component_id],
                        recommendations=[
                            "Specify which problems this component addresses",
                            "Provide clear problem-solution mapping",
                            "Remove component if not addressing any problems"
                        ]
                    ))
            
        except Exception as e:
            logger.error(f"VALIDATION: Problem mapping validation failed: {e}")
        
        return issues

    async def _validate_solution_feasibility(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        start_id: int
    ) -> List[ValidationIssue]:
        """Validate solution feasibility."""
        issues = []

        try:
            # Check resource requirements vs constraints
            if solution.complexity.value == "enterprise" and "budget limitations" in str(problem_analysis.solution_requirements.to_dict()).lower():
                issues.append(ValidationIssue(
                    issue_id=f"FEAS_{start_id:03d}",
                    category=ValidationCategory.FEASIBILITY,
                    severity="high",
                    title="Resource Constraint Mismatch",
                    description="Enterprise-level solution may exceed budget constraints",
                    affected_components=[comp.component_id for comp in solution.solution_components],
                    recommendations=[
                        "Consider phased implementation approach",
                        "Reduce solution complexity",
                        "Provide detailed cost-benefit analysis"
                    ]
                ))

            # Check timeline feasibility
            timeline_phases = solution.timeline_phases
            total_duration = sum(phase.get("duration_months", 0) for phase in timeline_phases)

            if total_duration > 24:  # More than 2 years
                issues.append(ValidationIssue(
                    issue_id=f"FEAS_{start_id + len(issues) + 1:03d}",
                    category=ValidationCategory.FEASIBILITY,
                    severity="medium",
                    title="Extended Timeline Risk",
                    description=f"Solution timeline of {total_duration} months may be too long",
                    affected_components=[],
                    recommendations=[
                        "Consider breaking into smaller phases",
                        "Identify quick wins and early deliverables",
                        "Provide interim value delivery milestones"
                    ]
                ))

            # Check technical feasibility
            for component in solution.solution_components:
                if not component.technical_details:
                    issues.append(ValidationIssue(
                        issue_id=f"FEAS_{start_id + len(issues) + 1:03d}",
                        category=ValidationCategory.FEASIBILITY,
                        severity="medium",
                        title="Missing Technical Details",
                        description=f"Component {component.name} lacks technical implementation details",
                        affected_components=[component.component_id],
                        recommendations=[
                            "Provide detailed technical specifications",
                            "Include implementation approach",
                            "Specify technology requirements"
                        ]
                    ))

        except Exception as e:
            logger.error(f"VALIDATION: Feasibility validation failed: {e}")

        return issues

    async def _validate_stakeholder_alignment(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        start_id: int
    ) -> List[ValidationIssue]:
        """Validate stakeholder alignment."""
        issues = []

        try:
            # Check if solution benefits address all stakeholder groups
            solution_stakeholders = set(solution.stakeholder_benefits.keys())
            expected_stakeholders = {"management", "end_users", "technical_staff"}

            missing_stakeholders = expected_stakeholders - solution_stakeholders

            if missing_stakeholders:
                issues.append(ValidationIssue(
                    issue_id=f"STAKE_{start_id:03d}",
                    category=ValidationCategory.STAKEHOLDER_ALIGNMENT,
                    severity="medium",
                    title="Missing Stakeholder Benefits",
                    description=f"Solution does not address benefits for: {list(missing_stakeholders)}",
                    affected_components=[],
                    recommendations=[
                        "Define benefits for all stakeholder groups",
                        "Ensure solution addresses diverse stakeholder needs",
                        "Include stakeholder-specific value propositions"
                    ]
                ))

            # Check for change management considerations
            has_training = any("training" in comp.implementation_steps for comp in solution.solution_components)
            if not has_training and solution.complexity.value in ["complex", "enterprise"]:
                issues.append(ValidationIssue(
                    issue_id=f"STAKE_{start_id + len(issues) + 1:03d}",
                    category=ValidationCategory.STAKEHOLDER_ALIGNMENT,
                    severity="medium",
                    title="Missing Change Management",
                    description="Complex solution lacks adequate training and change management",
                    affected_components=[],
                    recommendations=[
                        "Include comprehensive training program",
                        "Add change management components",
                        "Plan stakeholder engagement activities"
                    ]
                ))

        except Exception as e:
            logger.error(f"VALIDATION: Stakeholder alignment validation failed: {e}")

        return issues

    async def _validate_risk_assessment(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        start_id: int
    ) -> List[ValidationIssue]:
        """Validate risk assessment and mitigation."""
        issues = []

        try:
            # Check if risks are identified for each component
            for component in solution.solution_components:
                if not component.risks:
                    issues.append(ValidationIssue(
                        issue_id=f"RISK_{start_id + len(issues) + 1:03d}",
                        category=ValidationCategory.RISK_ASSESSMENT,
                        severity="medium",
                        title="Missing Risk Assessment",
                        description=f"Component {component.name} has no identified risks",
                        affected_components=[component.component_id],
                        recommendations=[
                            "Identify potential implementation risks",
                            "Assess technical and operational risks",
                            "Develop risk mitigation strategies"
                        ]
                    ))

            # Check if solution-level risk mitigation is adequate
            if len(solution.risk_mitigation) < 3:
                issues.append(ValidationIssue(
                    issue_id=f"RISK_{start_id:03d}",
                    category=ValidationCategory.RISK_ASSESSMENT,
                    severity="medium",
                    title="Insufficient Risk Mitigation",
                    description="Solution has insufficient risk mitigation strategies",
                    affected_components=[],
                    recommendations=[
                        "Develop comprehensive risk mitigation plan",
                        "Include contingency planning",
                        "Address technical, operational, and business risks"
                    ]
                ))

        except Exception as e:
            logger.error(f"VALIDATION: Risk assessment validation failed: {e}")

        return issues

    async def _validate_success_criteria(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        start_id: int
    ) -> List[ValidationIssue]:
        """Validate success criteria and metrics."""
        issues = []

        try:
            # Check if success metrics are measurable
            vague_metrics = ["improved", "enhanced", "better", "increased"]

            for metric in solution.success_metrics:
                if any(vague_term in metric.lower() for vague_term in vague_metrics) and "%" not in metric and "measurable" not in metric.lower():
                    issues.append(ValidationIssue(
                        issue_id=f"SUCCESS_{start_id + len(issues) + 1:03d}",
                        category=ValidationCategory.SUCCESS_CRITERIA,
                        severity="medium",
                        title="Vague Success Metric",
                        description=f"Success metric '{metric}' is not specific or measurable",
                        affected_components=[],
                        recommendations=[
                            "Define specific, measurable success criteria",
                            "Include quantitative targets and KPIs",
                            "Specify measurement methods and timelines"
                        ]
                    ))

            # Check if success criteria align with problem severity
            critical_problems = [p for p in problem_analysis.problems_identified if p.severity.value == "critical"]
            if critical_problems and len(solution.success_metrics) < 3:
                issues.append(ValidationIssue(
                    issue_id=f"SUCCESS_{start_id:03d}",
                    category=ValidationCategory.SUCCESS_CRITERIA,
                    severity="high",
                    title="Insufficient Success Metrics",
                    description="Critical problems require more comprehensive success metrics",
                    affected_components=[],
                    recommendations=[
                        "Define success metrics for each critical problem",
                        "Include both short-term and long-term metrics",
                        "Ensure metrics are aligned with problem severity"
                    ]
                ))

        except Exception as e:
            logger.error(f"VALIDATION: Success criteria validation failed: {e}")

        return issues

    async def _calculate_category_scores(
        self,
        validation_issues: List[ValidationIssue],
        problem_analysis: ProblemAnalysisResult,
        solution_generation: SolutionGenerationResult
    ) -> Dict[str, float]:
        """Calculate scores for each validation category."""
        category_scores = {}

        for category in ValidationCategory:
            category_issues = [issue for issue in validation_issues if issue.category == category]

            # Base score
            base_score = 0.8

            # Deduct points for issues
            for issue in category_issues:
                if issue.severity == "critical":
                    base_score -= 0.3
                elif issue.severity == "high":
                    base_score -= 0.2
                elif issue.severity == "medium":
                    base_score -= 0.1
                elif issue.severity == "low":
                    base_score -= 0.05

            category_scores[category.value] = max(base_score, 0.0)

        return category_scores

    def _determine_overall_result(
        self,
        overall_score: float,
        validation_issues: List[ValidationIssue]
    ) -> ValidationResult:
        """Determine overall validation result."""
        critical_issues = [issue for issue in validation_issues if issue.severity == "critical"]

        if critical_issues:
            return ValidationResult.FAIL
        elif overall_score >= 0.8:
            return ValidationResult.PASS
        elif overall_score >= 0.6:
            return ValidationResult.WARNING
        else:
            return ValidationResult.NEEDS_IMPROVEMENT

    async def _generate_validation_recommendations(
        self,
        validation_issues: List[ValidationIssue],
        problem_analysis: ProblemAnalysisResult,
        solution_generation: SolutionGenerationResult
    ) -> List[str]:
        """Generate overall validation recommendations."""
        recommendations = []

        # Group issues by category
        issues_by_category = {}
        for issue in validation_issues:
            category = issue.category.value
            if category not in issues_by_category:
                issues_by_category[category] = []
            issues_by_category[category].append(issue)

        # Generate category-specific recommendations
        if "problem_mapping" in issues_by_category:
            recommendations.append("Ensure all solution components clearly map to identified problems")

        if "effectiveness" in issues_by_category:
            recommendations.append("Enhance solution effectiveness by addressing root causes more directly")

        if "feasibility" in issues_by_category:
            recommendations.append("Review solution feasibility considering resource and timeline constraints")

        if "stakeholder_alignment" in issues_by_category:
            recommendations.append("Improve stakeholder alignment by addressing all stakeholder group needs")

        if "risk_assessment" in issues_by_category:
            recommendations.append("Strengthen risk assessment and mitigation planning")

        if "success_criteria" in issues_by_category:
            recommendations.append("Define more specific and measurable success criteria")

        # Add general recommendations
        recommendations.extend([
            "Conduct thorough review of all validation issues before proposal submission",
            "Consider iterative improvement based on validation feedback",
            "Ensure solution alignment with organizational goals and constraints"
        ])

        return recommendations

    async def _generate_improvement_suggestions(
        self,
        validation_issues: List[ValidationIssue],
        rejected_solutions: List[str],
        solution_generation: SolutionGenerationResult
    ) -> List[str]:
        """Generate improvement suggestions for rejected solutions."""
        suggestions = []

        if rejected_solutions:
            suggestions.append(f"Review and improve {len(rejected_solutions)} rejected solutions")

            # Analyze common issues in rejected solutions
            rejected_issues = [issue for issue in validation_issues
                             if any(sol_id in issue.affected_components for sol_id in rejected_solutions)]

            common_severities = {}
            for issue in rejected_issues:
                severity = issue.severity
                common_severities[severity] = common_severities.get(severity, 0) + 1

            if common_severities.get("critical", 0) > 0:
                suggestions.append("Address critical validation failures before resubmission")

            if common_severities.get("high", 0) > 2:
                suggestions.append("Focus on resolving high-severity issues across solutions")

            suggestions.extend([
                "Consider alternative solution approaches for rejected solutions",
                "Enhance problem-solution mapping for better alignment",
                "Improve technical feasibility and implementation planning"
            ])

        return suggestions
    
    async def _validate_solution_effectiveness(
        self,
        solution: UniqueSolution,
        problem_analysis: ProblemAnalysisResult,
        start_id: int
    ) -> List[ValidationIssue]:
        """Validate that solution will be effective in addressing problems."""
        issues = []
        
        try:
            # Use LLM to assess solution effectiveness
            system_prompt = """You are a Solution Effectiveness Validator.

Your task is to assess whether the proposed solution will effectively address the identified problems.

Evaluate:
1. Whether solution components directly address root causes
2. Whether the approach is likely to be effective
3. Whether the solution is comprehensive enough
4. Whether there are gaps in the solution approach
5. Whether the solution aligns with best practices

Return JSON with effectiveness assessment:
- overall_effectiveness: score 0-1
- effectiveness_issues: list of specific issues
- missing_elements: list of missing solution elements
- recommendations: list of improvement recommendations"""
            
            context = {
                "problems": [p.to_dict() for p in problem_analysis.problems_identified],
                "solution": solution.to_dict(),
                "requirements": problem_analysis.solution_requirements.to_dict()
            }
            
            user_prompt = f"""
            Assess the effectiveness of this solution in addressing the identified problems:
            
            {json.dumps(context, indent=2)}
            
            Return JSON assessment as specified.
            """
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            try:
                assessment = json.loads(response.content)
                effectiveness_score = assessment.get("overall_effectiveness", 0.5)
                
                if effectiveness_score < 0.6:
                    issues.append(ValidationIssue(
                        issue_id=f"EFF_{start_id:03d}",
                        category=ValidationCategory.EFFECTIVENESS,
                        severity="high" if effectiveness_score < 0.4 else "medium",
                        title="Low Solution Effectiveness",
                        description=f"Solution effectiveness score is {effectiveness_score:.2f}, indicating potential gaps",
                        affected_components=[comp.component_id for comp in solution.solution_components],
                        recommendations=assessment.get("recommendations", [
                            "Enhance solution components to better address root causes",
                            "Add missing solution elements",
                            "Improve solution comprehensiveness"
                        ])
                    ))
                
                # Add specific effectiveness issues
                for i, issue_desc in enumerate(assessment.get("effectiveness_issues", [])):
                    issues.append(ValidationIssue(
                        issue_id=f"EFF_{start_id + i + 1:03d}",
                        category=ValidationCategory.EFFECTIVENESS,
                        severity="medium",
                        title="Effectiveness Concern",
                        description=issue_desc,
                        affected_components=[],
                        recommendations=["Address effectiveness concern", "Improve solution approach"]
                    ))
                
            except json.JSONDecodeError:
                logger.warning("Failed to parse effectiveness assessment JSON")
                
        except Exception as e:
            logger.error(f"VALIDATION: Effectiveness validation failed: {e}")
        
        return issues
