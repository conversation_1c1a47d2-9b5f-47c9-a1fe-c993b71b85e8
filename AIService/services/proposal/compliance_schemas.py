import json
import re
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum
from loguru import logger


class VolumeTitle(str, Enum):
    """Enumeration of valid volume titles for government proposals"""
    VOLUME_I = "Volume I"
    VOLUME_II = "Volume II"
    VOLUME_III = "Volume III"
    VOLUME_IV = "Volume IV"
    VOLUME_V = "Volume V"
    RFI = "RFI"

    @classmethod
    def extract_base_volume(cls, volume_title: str) -> Optional['VolumeTitle']:
        """
        Extract the base volume from a title that may contain additional descriptive text.
        
        Args:
            volume_title: The full volume title (e.g., "Volume I - Technical Capability")
            
        Returns:
            The corresponding VolumeTitle enum value if found, None otherwise
        """
        if not volume_title:
            return None
        
        # Normalize the input
        title_normalized = volume_title.strip()
        
        # Direct match first
        for volume in cls:
            if volume.value.lower() == title_normalized.lower():
                return volume
        
        # Pattern matching for volumes with additional text
        patterns = [
            r'^volume\s+i(?:\s|$|-)',     # Volume I (with space, dash, or end)
            r'^volume\s+ii(?:\s|$|-)',    # Volume II
            r'^volume\s+iii(?:\s|$|-)',   # Volume III  
            r'^volume\s+iv(?:\s|$|-)',    # Volume IV
            r'^volume\s+v(?:\s|$|-)',     # Volume V
            r'^rfi(?:\s|$|-)'             # RFI
        ]
        
        volume_map = [
            cls.VOLUME_I,
            cls.VOLUME_II, 
            cls.VOLUME_III,
            cls.VOLUME_IV,
            cls.VOLUME_V,
            cls.RFI
        ]
        
        for pattern, volume in zip(patterns, volume_map):
            if re.match(pattern, title_normalized.lower()):
                return volume
        
        return None


class StructureSection(BaseModel):
    """Model for individual sections within a proposal volume"""
    section_name: str = Field(
        description="Specific content area or section title as required by solicitation",
        min_length=1
    )
    page_limit: int = Field(
        description="Pages allocated to each section (must be at least 1)",
        ge=1
    )

    @field_validator('section_name')
    @classmethod
    def validate_section_name(cls, v):
        if not v or v.strip() == "":
            raise ValueError("Section name cannot be empty")
        return v.strip()

    @field_validator('page_limit', mode='before')
    @classmethod
    def validate_page_limit(cls, v):
        """Handle flexible page limit inputs for sections"""
        if isinstance(v, int):
            if v < 1:
                raise ValueError("Page limit must be at least 1")
            return v
        
        if isinstance(v, str):
            v_clean = v.strip().upper()
            
            # For sections, we're more strict - they should have actual limits
            if v_clean in ['N/A', 'NA', 'TBD', 'TO BE DETERMINED', '']:
                raise ValueError("Section page limit must be specified (cannot be N/A or empty)")
            
            try:
                parsed_int = int(v_clean)
                if parsed_int < 1:
                    raise ValueError("Page limit must be at least 1")
                return parsed_int
            except ValueError:
                raise ValueError(f"Page limit '{v}' is not a valid integer")
        
        try:
            parsed_int = int(v)
            if parsed_int < 1:
                raise ValueError("Page limit must be at least 1")
            return parsed_int
        except (ValueError, TypeError):
            raise ValueError(f"Page limit '{v}' could not be parsed as an integer")


class StructureVolume(BaseModel):
    """Model for proposal volumes containing sections"""
    volume_title: str = Field(
        description="Exact volume name from solicitation",
        min_length=1
    )
    content: List[StructureSection] = Field(
        description="Array of required sections within each volume",
        min_length=1
    )
    total_page_limit: Optional[int] = Field(
        description="Total page limit for the entire volume (if specified in solicitation)",
        ge=1,
        default=None
    )

    @field_validator('total_page_limit', mode='before')
    @classmethod
    def validate_total_page_limit(cls, v):
        """Handle flexible page limit inputs including N/A, TBD, etc."""
        if v is None:
            return None
        
        # If it's already an integer, validate it
        if isinstance(v, int):
            if v < 1:
                raise ValueError("Page limit must be at least 1")
            return v
        
        # Handle string inputs
        if isinstance(v, str):
            v_clean = v.strip().upper()
            
            # Handle common "not specified" values
            na_values = ['N/A', 'NA', 'NOT APPLICABLE', 'NOT SPECIFIED', 'TBD', 
                        'TO BE DETERMINED', 'NONE', 'NO LIMIT', 'UNLIMITED', '']
            
            if v_clean in na_values:
                return None
            
            # Try to parse as integer
            try:
                parsed_int = int(v_clean)
                if parsed_int < 1:
                    raise ValueError("Page limit must be at least 1")
                return parsed_int
            except ValueError:
                raise ValueError(f"Page limit '{v}' is not a valid integer or recognized N/A value")
        
        # Handle other types by trying to convert to int
        try:
            parsed_int = int(v)
            if parsed_int < 1:
                raise ValueError("Page limit must be at least 1")
            return parsed_int
        except (ValueError, TypeError):
            raise ValueError(f"Page limit '{v}' could not be parsed as an integer")

    @field_validator('volume_title')
    @classmethod
    def validate_volume_title(cls, v):
        if not v or v.strip() == "":
            raise ValueError("Volume title cannot be empty")
        
        # COMMENTED OUT: Check if the volume title can be mapped to a valid base volume
        # base_volume = VolumeTitle.extract_base_volume(v)
        # if base_volume is None:
        #     raise ValueError(f"Volume title '{v}' does not match any valid volume pattern (Volume I-V or RFI)")
        
        return v.strip()

    @model_validator(mode='after')
    def validate_page_limits(self):
        """Ensure section page limits don't exceed total volume limit"""
        if not self.content:
            raise ValueError("Volume must contain at least one section")

        total_section_pages = sum(section.page_limit for section in self.content)

        if self.total_page_limit and total_section_pages > self.total_page_limit:
            raise ValueError(
                f"Total section pages ({total_section_pages}) exceed volume limit ({self.total_page_limit})"
            )

        return self


class StructureComplianceSchema(BaseModel):
    """Complete structure compliance schema for government proposals"""
    structure: List[StructureVolume] = Field(
        description="Array of volumes required by the solicitation",
        min_length=1
    )

    @field_validator('structure')
    @classmethod
    def validate_structure(cls, v):
        if not v:
            raise ValueError("Structure must contain at least one volume")

        # COMMENTED OUT: Check for duplicate base volume titles (ignoring descriptive text)
        # base_volumes = []
        # for vol in v:
        #     base_volume = VolumeTitle.extract_base_volume(vol.volume_title)
        #     if base_volume:
        #         base_volumes.append(base_volume)
        # 
        # if len(base_volumes) != len(set(base_volumes)):
        #     raise ValueError("Duplicate base volume titles are not allowed (e.g., multiple Volume I entries)")

        return v


class ContentCompliance(BaseModel):
    """Model for content requirements within a specific volume"""
    volume_title: str = Field(
        description="The volume that is expected to be seen in the response (flexible format)"
    )
    content: str = Field(
        description="The expected content to be seen in the response for this volume",
        min_length=10
    )
    page_limit: Optional[int] = Field(
        description="Page limit for this volume (if specified in solicitation)",
        ge=1,
        default=None
    )
    evaluation_criteria: Optional[List[str]] = Field(
        description="Specific evaluation criteria or factors for this volume with scoring details",
        default=None
    )
    mandatory_sections: Optional[List[str]] = Field(
        description="List of mandatory sections that must be included in this volume",
        default=None
    )
    technical_requirements: Optional[List[str]] = Field(
        description="Specific technical specifications and constraints for this volume",
        default=None
    )
    compliance_requirements: Optional[List[str]] = Field(
        description="Mandatory compliance and regulatory requirements for this volume",
        default=None
    )
    scoring_methodology: Optional[str] = Field(
        description="Description of how this volume will be evaluated and scored",
        default=None
    )

    @field_validator('volume_title')
    @classmethod
    def validate_volume_title(cls, v):
        if not v or v.strip() == "":
            raise ValueError("Volume title cannot be empty")
        
        # COMMENTED OUT: Check if the volume title can be mapped to a valid base volume
        # base_volume = VolumeTitle.extract_base_volume(v)
        # if base_volume is None:
        #     raise ValueError(f"Volume title '{v}' does not match any valid volume pattern (Volume I-V or RFI)")
        
        return v.strip()

    @field_validator('content')
    @classmethod
    def validate_content(cls, v):
        if not v or v.strip() == "":
            raise ValueError("Content description cannot be empty")

        # Check for placeholder content
        placeholder_indicators = [
            "[insert", "[add", "[provide", "[include",
            "tbd", "to be determined", "xxx", "placeholder"
        ]
        content_lower = v.lower()
        for indicator in placeholder_indicators:
            if indicator in content_lower:
                raise ValueError(f"Content contains placeholder text: {indicator}")
        return v.strip()

    def get_base_volume_title(self) -> VolumeTitle:
        """Get the base volume title enum value"""
        return VolumeTitle.extract_base_volume(self.volume_title)


class ContentComplianceResponse(BaseModel):
    """Complete content compliance response schema"""
    content_compliance: List[ContentCompliance] = Field(
        description="List of content requirements organized by volume",
        min_length=1
    )

    @field_validator('content_compliance')
    @classmethod
    def validate_compliance_list(cls, v):
        if not v:
            raise ValueError("Content compliance must contain at least one volume")

        # COMMENTED OUT: Check for duplicate base volume titles (ignoring descriptive text)
        # base_volumes = []
        # for item in v:
        #     base_volume = VolumeTitle.extract_base_volume(item.volume_title)
        #     if base_volume:
        #         base_volumes.append(base_volume)
        # 
        # if len(base_volumes) != len(set(base_volumes)):
        #     raise ValueError("Duplicate base volume titles are not allowed (e.g., multiple Volume I entries)")

        return v


class ComplianceValidator:
    """Utility class for validating compliance schemas"""
    
    @staticmethod
    def validate_structure_compliance(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate structure compliance data against schema
        
        Args:
            data: Dictionary containing structure compliance data
            
        Returns:
            Dictionary with validation results
        """
        try:
            validated = StructureComplianceSchema(**data)
            return {
                "is_valid": True,
                "validated_data": validated.model_dump(),
                "errors": None
            }
        except Exception as e:
            logger.error(f"Structure compliance validation failed: {e}")
            return {
                "is_valid": False,
                "validated_data": None,
                "errors": str(e)
            }
    
    @staticmethod
    def validate_content_compliance(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate content compliance data against schema
        
        Args:
            data: Dictionary containing content compliance data
            
        Returns:
            Dictionary with validation results
        """
        try:
            validated = ContentComplianceResponse(**data)
            return {
                "is_valid": True,
                "validated_data": validated.model_dump(),
                "errors": None
            }
        except Exception as e:
            logger.error(f"Content compliance validation failed: {e}")
            return {
                "is_valid": False,
                "validated_data": None,
                "errors": str(e)
            }
    
    @staticmethod
    def get_structure_schema_example() -> Dict[str, Any]:
        """Get example structure compliance schema"""
        return {
            "structure": [
                {
                    "volume_title": "Volume I - Technical Capability",
                    "total_page_limit": 20,
                    "content": [
                        {
                            "section_name": "Technical Approach",
                            "page_limit": 8
                        },
                        {
                            "section_name": "Management Plan",
                            "page_limit": 6
                        },
                        {
                            "section_name": "Past Performance",
                            "page_limit": 6
                        }
                    ]
                },
                {
                    "volume_title": "Volume II - Price",
                    "total_page_limit": 10,
                    "content": [
                        {
                            "section_name": "Cost Proposal",
                            "page_limit": 5
                        },
                        {
                            "section_name": "Pricing Schedule",
                            "page_limit": 5
                        }
                    ]
                }
            ]
        }
    
    @staticmethod
    def get_content_schema_example() -> Dict[str, Any]:
        """Get example content compliance schema"""
        return {
            "content_compliance": [
                {
                    "volume_title": "Volume I - Technical Capability",
                    "content": "Technical approach demonstrating understanding of requirements, proposed methodology, implementation timeline, risk mitigation strategies, and quality assurance measures.",
                    "page_limit": 20,
                    "evaluation_criteria": [
                        "Technical understanding (30 points)",
                        "Proposed methodology (25 points)",
                        "Risk management (20 points)",
                        "Past performance (25 points)"
                    ],
                    "mandatory_sections": [
                        "Technical Approach",
                        "Management Plan",
                        "Past Performance"
                    ],
                    "technical_requirements": [
                        "Cloud-based architecture",
                        "FISMA compliance",
                        "99.9% uptime requirement",
                        "API integration capabilities"
                    ],
                    "compliance_requirements": [
                        "Section 508 accessibility",
                        "NIST cybersecurity framework",
                        "FedRAMP authorization"
                    ],
                    "scoring_methodology": "Technical capability will be evaluated using a 100-point scale with emphasis on demonstrated experience and innovative approaches"
                },
                {
                    "volume_title": "Volume II - Price",
                    "content": "Cost proposal with detailed pricing breakdown, cost justification, and supporting documentation for all proposed services.",
                    "page_limit": 10,
                    "evaluation_criteria": [
                        "Price reasonableness",
                        "Cost realism"
                    ],
                    "mandatory_sections": [
                        "Cost Proposal",
                        "Pricing Schedule"
                    ]
                }
            ]
        }
