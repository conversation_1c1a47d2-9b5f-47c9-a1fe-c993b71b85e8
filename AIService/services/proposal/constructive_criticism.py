"""
Proposal Volume Criticism Service

This service generates constructive criticism for proposal volumes immediately after generation.
It analyzes the generated proposals against the original outlines and requirements to provide
actionable feedback on what's missing, what could be improved, and what additional information
would strengthen the proposal.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from pydantic import BaseModel, Field
from sqlalchemy import select

from services.llm.llm_factory import get_llm
from controllers.customer.datametastore_controller import DataMetastoreController
from database import get_customer_db
from models.customer_models import DataMetastore


class CriticismInsight(BaseModel):
    """Individual criticism insight with specific feedback"""
    category: str = Field(description="Category of criticism (e.g., 'Missing Information', 'Content Gap', 'Technical Depth')")
    severity: str = Field(description="Severity level: 'Critical', 'Important', 'Minor', 'Suggestion'")
    description: str = Field(description="Detailed description of the issue or gap")
    recommendation: str = Field(description="Specific recommendation for improvement")
    required_documents: List[str] = Field(description="List of documents/information that should be provided")
    impact: str = Field(description="Potential impact on proposal success")


class VolumeCriticism(BaseModel):
    """Criticism analysis for a single volume"""
    volume_number: int = Field(description="Volume number being analyzed")
    overall_score: int = Field(description="Overall quality score from 0-100")
    completeness_score: int = Field(description="Completeness score from 0-100")
    technical_depth_score: int = Field(description="Technical depth score from 0-100")
    compliance_score: int = Field(description="Compliance score from 0-100")
    
    strengths: List[str] = Field(description="List of proposal strengths")
    insights: List[CriticismInsight] = Field(description="List of criticism insights")
    
    missing_sections: List[str] = Field(description="Sections that should be added")
    insufficient_detail: List[str] = Field(description="Sections that need more detail")
    compliance_gaps: List[str] = Field(description="Areas where compliance requirements are not met")
    
    priority_improvements: List[str] = Field(description="Top priority improvements needed")
    suggested_documents: List[str] = Field(description="Additional documents that would strengthen the proposal")


class ProposalCriticismReport(BaseModel):
    """Complete criticism report for all volumes"""
    opportunity_id: str = Field(description="Opportunity ID")
    tenant_id: str = Field(description="Tenant ID")
    analysis_timestamp: datetime = Field(description="When analysis was performed")
    
    volumes: List[VolumeCriticism] = Field(description="Criticism for each volume")
    
    overall_assessment: str = Field(description="Overall assessment summary")
    critical_gaps: List[str] = Field(description="Most critical gaps across all volumes")
    recommended_actions: List[str] = Field(description="Recommended immediate actions")
    
    executive_summary: str = Field(description="Executive summary for stakeholders")


class ProposalVolumeCriticismService:
    """Service for generating constructive criticism of proposal volumes"""
    
    def __init__(self, llm_api_url: str = "http://ai.kontratar.com:11434"):
        """Initialize the criticism service with LLM configuration"""
        self.llm = get_llm(
            temperature=0.1,
            timeout=180,
            num_ctx=8192,
            num_predict=2048,
            base_url=llm_api_url
        )
        
        # Create structured output models
        self.volume_criticism_model = self.llm.with_structured_output(VolumeCriticism)
        self.report_model = self.llm.with_structured_output(ProposalCriticismReport)
    
    async def _get_next_version_number(self, db, base_identifier: str, tenant_id: str) -> int:
        """Get the next version number by checking existing records"""
        try:            
            pattern = f"{base_identifier}_v%"
            
            query = select(DataMetastore.record_identifier).where(
                DataMetastore.record_identifier.like(pattern),
                DataMetastore.tenant_id == tenant_id
            )
            
            result = await db.execute(query)
            existing_identifiers = [row[0] for row in result.fetchall()]
            
            if not existing_identifiers:
                return 1
            
            # Extract version numbers
            version_numbers = []
            version_prefix = f"{base_identifier}_v"
            
            for identifier in existing_identifiers:
                if identifier.startswith(version_prefix):
                    version_part = identifier[len(version_prefix):]
                    try:
                        version_numbers.append(int(version_part))
                    except ValueError:
                        continue
            
            return max(version_numbers) + 1 if version_numbers else 1
            
        except Exception as e:
            logger.error(f"Error getting next version number for {base_identifier}: {e}")
            return 1
    
    async def generate_volume_criticism(
        self,
        opportunity_id: str,
        tenant_id: str,
        proposal_volumes: List[List[Dict[str, Any]]],
        all_outlines: List[List[Dict[str, Any]]]
    ) -> Optional[str]:
        """
        Generate comprehensive criticism for all proposal volumes
        
        Args:
            opportunity_id: The opportunity ID
            tenant_id: The tenant ID
            proposal_volumes: Generated proposal volumes
            all_outlines: Original outlines used for generation
            
        Returns:
            Record identifier for the stored criticism report, or None if failed
        """
        try:
            logger.info(f"Starting criticism analysis for opportunity {opportunity_id}")
            
            # Analyze each volume
            volume_criticisms = []
            for idx, (volume_data, outline) in enumerate(zip(proposal_volumes, all_outlines)):
                if volume_data is not None and outline is not None:
                    volume_num = idx + 1
                    logger.info(f"Analyzing volume {volume_num}")
                    
                    criticism = await self._analyze_single_volume(
                        volume_number=volume_num,
                        volume_data=volume_data,
                        outline=outline
                    )
                    
                    if criticism:
                        volume_criticisms.append(criticism)
            
            if not volume_criticisms:
                logger.warning(f"No volume criticisms generated for {opportunity_id}")
                return None
            
            # Generate overall report
            report = await self._generate_comprehensive_report(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                volume_criticisms=volume_criticisms
            )
            
            if not report:
                logger.error(f"Failed to generate comprehensive report for {opportunity_id}")
                return None
            
            # Convert to PDF and store
            record_identifier = await self._store_criticism_report(
                report=report,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id
            )
            
            logger.info(f"Successfully generated and stored criticism for {opportunity_id}")
            return record_identifier
            
        except Exception as e:
            logger.error(f"Error generating volume criticism for {opportunity_id}: {e}")
            return None
    
    async def _analyze_single_volume(
        self,
        volume_number: int,
        volume_data: List[Dict[str, Any]],
        outline: List[Dict[str, Any]]
    ) -> Optional[VolumeCriticism]:
        """Analyze a single volume and generate criticism"""
        try:
            system_prompt = self._get_volume_analysis_system_prompt()
            user_prompt = self._build_volume_analysis_prompt(
                volume_number=volume_number,
                volume_data=volume_data,
                outline=outline
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            analysis = self.volume_criticism_model.invoke(messages)
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing volume {volume_number}: {e}")
            return None
    
    async def _generate_comprehensive_report(
        self,
        opportunity_id: str,
        tenant_id: str,
        volume_criticisms: List[VolumeCriticism]
    ) -> Optional[ProposalCriticismReport]:
        """Generate comprehensive criticism report"""
        try:
            system_prompt = self._get_report_generation_system_prompt()
            user_prompt = self._build_report_generation_prompt(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                volume_criticisms=volume_criticisms
            )
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Get structured report
            report = self.report_model.invoke(messages)
            return report
            
        except Exception as e:
            logger.error(f"Error generating comprehensive report: {e}")
            return None
    
    async def _store_criticism_report(
        self,
        report: ProposalCriticismReport,
        opportunity_id: str,
        tenant_id: str
    ) -> Optional[str]:
        """Store criticism report as PDF in datametastore with versioning"""
        try:
            report_markdown = self._format_report_for_pdf(report)
            
            from services.exports.generate_pdf_bytes import PDFGenerator
            
            pdf_bytes, success_message = PDFGenerator.generate_pdf(
                markdown_content=report_markdown,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                cover_page_elements=None,
                toc_data=None,
                trailing_page_markdown=None,
                compliance=None,
                volume_number=1,
                image_only=False
            )
            
            if not pdf_bytes:
                logger.error(f"Failed to generate PDF: {success_message}")
                return None
            
            # Implement versioning pattern
            async for db in get_customer_db():
                # Create base identifier for criticism reports
                base_identifier = f"{opportunity_id}_criticism"
                version_number = await self._get_next_version_number(db, base_identifier, tenant_id)
                
                # Create versioned record identifier and filename
                criticism_record_identifier = f"{base_identifier}_v{version_number}"
                versioned_filename = f"proposal_criticism_{opportunity_id}_v{version_number}.pdf"
                
                # Always create new versioned record
                stored_record = await DataMetastoreController.add(
                    db=db,
                    record_identifier=criticism_record_identifier,
                    record_type="PROPOSAL_FEEDBACK",
                    tenant_id=tenant_id,
                    original_document_content_type="application/pdf",
                    original_document_file_name=versioned_filename,
                    original_document=pdf_bytes,
                    owner="SYSTEM"
                )
                
                if stored_record:
                    logger.info(f"Created new criticism record for opportunity {opportunity_id}, version {version_number} with ID: {stored_record.id}")
                    return criticism_record_identifier
                else:
                    logger.error(f"Failed to create criticism record for opportunity {opportunity_id}, version {version_number}")
                    return None
                break
            
        except Exception as e:
            logger.error(f"Error storing criticism report: {e}")
            return None
    
    def _get_volume_analysis_system_prompt(self) -> str:
        """Get system prompt for volume analysis"""
        return """You are an expert proposal analyst with extensive experience in government contracting and proposal evaluation. Your role is to provide constructive, actionable criticism that helps improve proposal quality and win probability.

Your analysis should be:
- Constructive and specific
- Focused on actionable improvements
- Aligned with government contracting best practices
- Detailed about missing information and gaps
- Clear about the impact of deficiencies

Focus on identifying:
1. Missing required information or sections
2. Insufficient technical depth or detail
3. Compliance gaps with standard proposal requirements
4. Areas where additional documentation would strengthen the proposal
5. Content that doesn't adequately address typical evaluation criteria

Provide specific recommendations for each issue you identify."""
    
    def _build_volume_analysis_prompt(
        self,
        volume_number: int,
        volume_data: List[Dict[str, Any]],
        outline: List[Dict[str, Any]]
    ) -> str:
        """Build detailed prompt for volume analysis"""
        
        prompt = f"""Please analyze Volume {volume_number} of this government proposal and provide constructive criticism.

## ORIGINAL OUTLINE (What was planned):
{json.dumps(outline, indent=2)[:2000]}

## GENERATED VOLUME CONTENT:
{json.dumps(volume_data, indent=2)[:3000]}

## ANALYSIS INSTRUCTIONS:
Analyze this volume against the outline and best practices for government proposals. Identify:

1. **Missing Content**: What sections or information are missing compared to the outline?
2. **Insufficient Detail**: Where does the content lack necessary depth?
3. **Compliance Gaps**: What standard government proposal requirements are not adequately addressed?
4. **Additional Documentation Needed**: What supporting documents would strengthen this volume?
5. **Technical Depth Issues**: Where is more technical detail needed?

Provide specific, actionable recommendations for each gap you identify. Focus on what would make this proposal more competitive and compliant with typical government contracting standards."""
        
        return prompt
    
    def _get_report_generation_system_prompt(self) -> str:
        """Get system prompt for comprehensive report generation"""
        return """You are a senior proposal manager creating an executive summary and comprehensive analysis report. Your report will be used by proposal teams to prioritize improvements and by executives to understand proposal readiness.

Create a professional, actionable report that:
- Provides clear executive summary for decision makers
- Identifies critical gaps that could impact win probability
- Prioritizes improvements by impact and effort required
- Gives specific next steps and recommended actions
- Maintains a constructive, solution-focused tone

Your analysis should help the team understand what needs immediate attention versus what can be addressed in future iterations."""
    
    def _build_report_generation_prompt(
        self,
        opportunity_id: str,
        tenant_id: str,
        volume_criticisms: List[VolumeCriticism]
    ) -> str:
        """Build prompt for comprehensive report generation"""
        
        # Serialize volume criticisms for analysis
        criticisms_data = []
        for criticism in volume_criticisms:
            criticisms_data.append(criticism.model_dump())
        
        prompt = f"""Generate a comprehensive criticism report for this government proposal.

## OPPORTUNITY INFORMATION:
**ID:** {opportunity_id}
**Tenant:** {tenant_id}

## INDIVIDUAL VOLUME ANALYSES:
{json.dumps(criticisms_data, indent=2, default=str)}

## REPORT REQUIREMENTS:
Create a comprehensive report that includes:

1. **Executive Summary**: High-level assessment for leadership
2. **Critical Gaps**: Most important issues across all volumes
3. **Overall Assessment**: Proposal readiness and competitiveness
4. **Recommended Actions**: Prioritized next steps

The report should help stakeholders understand:
- Current proposal strength and readiness
- Risk areas that could impact win probability
- Specific actions needed to improve competitiveness
- Priority order for addressing identified gaps

Focus on actionable insights that will drive meaningful improvements to the proposal."""
        
        return prompt
    
    def _format_report_for_pdf(self, report: ProposalCriticismReport) -> str:
        """Format the criticism report for PDF generation as markdown"""
        
        # Calculate overall scores
        volume_scores = [v.overall_score for v in report.volumes if hasattr(v, 'overall_score')]
        avg_score = sum(volume_scores) / len(volume_scores) if volume_scores else 0
        
        # Determine grade
        if avg_score >= 90:
            grade = "A+ (Excellent)"
        elif avg_score >= 80:
            grade = "A (Very Good)"
        elif avg_score >= 70:
            grade = "B (Good)"
        elif avg_score >= 60:
            grade = "C (Fair)"
        elif avg_score >= 50:
            grade = "D (Poor)"
        else:
            grade = "F (Needs Major Work)"
        
        # Format as markdown for PDFGenerator
        formatted_markdown = f"""# PROPOSAL CRITICISM REPORT

**Opportunity ID:** {report.opportunity_id}  
**Analysis Date:** {report.analysis_timestamp.strftime('%B %d, %Y at %I:%M %p')}  
**Overall Grade:** {grade} ({avg_score:.1f}/100)

## EXECUTIVE SUMMARY

{report.executive_summary}

## OVERALL ASSESSMENT

{report.overall_assessment}

## CRITICAL GAPS IDENTIFIED

"""
        
        for i, gap in enumerate(report.critical_gaps, 1):
            formatted_markdown += f"{i}. {gap}\n"
        
        formatted_markdown += "\n## RECOMMENDED IMMEDIATE ACTIONS\n\n"
        
        for i, action in enumerate(report.recommended_actions, 1):
            formatted_markdown += f"{i}. {action}\n"
        
        formatted_markdown += "\n## DETAILED VOLUME ANALYSIS\n\n"
        
        for volume in report.volumes:
            formatted_markdown += f"### Volume {volume.volume_number} Analysis\n\n"
            formatted_markdown += f"**Overall Score:** {volume.overall_score}/100  \n"
            formatted_markdown += f"**Completeness:** {volume.completeness_score}/100  \n"
            formatted_markdown += f"**Technical Depth:** {volume.technical_depth_score}/100  \n"
            formatted_markdown += f"**Compliance:** {volume.compliance_score}/100\n\n"
            
            if volume.strengths:
                formatted_markdown += "**Strengths:**\n\n"
                for strength in volume.strengths:
                    formatted_markdown += f"- {strength}\n"
                formatted_markdown += "\n"
            
            if volume.insights:
                formatted_markdown += "**Key Issues:**\n\n"
                for insight in volume.insights:
                    formatted_markdown += f"- **{insight.category}** ({insight.severity}): {insight.description}\n"
                    formatted_markdown += f"  - *Recommendation:* {insight.recommendation}\n"
                    if insight.required_documents:
                        formatted_markdown += f"  - *Documents Needed:* {', '.join(insight.required_documents)}\n"
                    formatted_markdown += "\n"
            
            if volume.priority_improvements:
                formatted_markdown += "**Priority Improvements:**\n\n"
                for improvement in volume.priority_improvements:
                    formatted_markdown += f"- {improvement}\n"
                formatted_markdown += "\n"
            
            if volume.suggested_documents:
                formatted_markdown += "**Suggested Additional Documents:**\n\n"
                for doc in volume.suggested_documents:
                    formatted_markdown += f"- {doc}\n"
                formatted_markdown += "\n"
            
            formatted_markdown += "---\n\n"
        
        formatted_markdown += f"""## CONCLUSION

This analysis provides a comprehensive review of the proposal's current state and identifies specific areas for improvement. The recommendations are prioritized by impact on win probability and should be addressed in the order presented.

For questions about this analysis or to discuss specific recommendations, please contact the proposal team.

---

*Report generated by AI-powered Proposal Analysis System*  
*Analysis Date: {report.analysis_timestamp.strftime('%B %d, %Y at %I:%M %p')}*
"""
        
        return formatted_markdown


# Simplified integration function for the scheduler
async def generate_proposal_criticism(
    opportunity_id: str,
    tenant_id: str,
    proposal_volumes: List[List[Dict[str, Any]]],
    all_outlines: List[List[Dict[str, Any]]]
) -> Optional[str]:
    """
    Simplified convenience function to generate criticism using only the specified parameters
    
    Args:
        opportunity_id: The opportunity ID
        tenant_id: The tenant ID  
        proposal_volumes: Generated proposal volumes
        all_outlines: Original outlines used for generation
    
    Returns:
        Record identifier of stored criticism report, or None if failed
    """
    service = ProposalVolumeCriticismService()
    return await service.generate_volume_criticism(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        proposal_volumes=proposal_volumes,
        all_outlines=all_outlines
    )