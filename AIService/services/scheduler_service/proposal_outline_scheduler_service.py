import asyncio

from controllers.customer.proposal_outline_queue_controller import ProposalOutlineQueueController
from services.scheduler_service.schedule_lock_service import ScheduleLockService
from controllers.customer.datametastore_controller import DataMetastoreController

from loguru import logger
from database import get_kontratar_db, get_customer_db
from models.customer_models import ProposalOutlineQueue
from services.proposal.outline import ProposalOutlineService
from utils.semaphore import run_with_semaphore

from models.customer_models import CustomOppsQueue as QueueItem

from sqlalchemy import select, text
from controllers.kontratar.sam_opps_table_coontroller import OppsController
import json
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.custom_opportunity_table_info_controller import CustomOpportunityTableInfoController
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
import ast


class ProposalOutlineSchedulerService:
    def __init__(self):
        self.outline_service = ProposalOutlineService()
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = False
        self.lock_service = ScheduleLockService("PROPOSAL_OUTLINE_SCHEDULER_LOCK", "PROPOSAL_OUTLINE_SCHEDULER")
        self.proposal_outline_service = ProposalOutlineService()    

    async def process_outline_queue(self):
        if not self.is_enabled:
            logger.info("Proposal Outline Scheduler is disabled.")
            return

        acquired = await self.lock_service.try_acquire_lock()
        if not acquired:
            logger.info("Proposal Outline Scheduler lock not acquired, skipping this run")
            return

        try:
            async for db in get_kontratar_db():
                items = await ProposalOutlineQueueController.claim_new_queue_items(db, limit=6)
                break

            if not items:
                logger.info("No outline queue items to process.")
                return
            
            async def _on_enter(item):
                async for db in get_kontratar_db():
                    await ProposalOutlineQueueController.update_queue_status(
                        db, item.id, "PROCESSING"
                    )
                    break

            async def worker(item):
                try:
                    outline_type = (item.outline_type or "").lower()
                    if outline_type == "sam":
                        await self._generate_proposal_outline_sam(item)
                    elif outline_type == "custom":
                        await self._generate_proposal_outline_custom(item)
                    else:
                        logger.warning(f"Unknown outline_type '{item.outline_type}' for item {item.opps_id}, skipping.")
                        await ProposalOutlineQueueController.update_queue_status(
                            db, item.id, "FAILED", error_message="Unknown outline_type"
                        )
                        return
                    
                    
                    async for db in get_kontratar_db():
                        await ProposalOutlineQueueController.complete_and_notify(db, item.id, item.outline_type)
                        break
                    logger.info(f"Outline generated for {item.opps_id}")
                except Exception as e:
                    logger.error(f"Error generating outline for {item.opps_id}: {e}")
                    async for db in get_kontratar_db():
                        await ProposalOutlineQueueController.update_queue_status(
                            db, item.id, "FAILED", error_message=str(e)
                        )
                        break
                    
            await run_with_semaphore(
                items=items,
                max_jobs=2,
                on_enter=_on_enter,
                worker=worker
            )
        finally:
            await self.lock_service.release_lock()

    async def _extract_and_save_references(self, outline_result: dict, opportunity_id: str, tenant_id: str, volume_number: int = 1, volume_toc: Optional[list] = None):
        """Extract references from outline data and save to datametastore database as PDF bytes with TOC numbering"""
        try:
            if not outline_result or "outlines" not in outline_result:
                logger.info(f"No outline data found for opportunity {opportunity_id}, skipping references extraction")
                return

            toc_numbering = {}
            if volume_toc:
                for toc_item in volume_toc:
                    title = toc_item.get("title", "")
                    number = toc_item.get("number", "")
                    if title and number:
                        toc_numbering[title] = number

            all_references = []
            for section in outline_result["outlines"]:
                title = section.get("title", "")
                references = section.get("references", [])

                # Get section number from TOC or use default
                section_number = toc_numbering.get(title, "")

                # Collect references for separate storage
                if references:
                    section_refs = {
                        "section_number": section_number,
                        "section_title": title,
                        "references": references
                    }
                    all_references.append(section_refs)

            if not all_references:
                logger.info(f"No references found for opportunity {opportunity_id}")
                return

            references_content_lines = []
            references_content_lines.append(f"# References - Volume {volume_number}")
            references_content_lines.append("")
            
            for section_refs in all_references:
                section_number = section_refs.get("section_number", "")
                section_title = section_refs.get("section_title", "")
                references = section_refs.get("references", [])
                
                if references:
                    header_title = f"{section_number} {section_title}" if section_number else section_title
                    references_content_lines.append(f"## {header_title} - References")
                    references_content_lines.append("")
                    
                    for i, reference in enumerate(references, 1):
                        cleaned_ref = reference.strip().replace('"', '').replace("'", "'")
                        references_content_lines.append(f"{i}. {cleaned_ref}")
                    
                    references_content_lines.append("")

            # Join all content into markdown
            references_markdown = "\n".join(references_content_lines)
            
            # Convert markdown to PDF bytes using PDFGenerator
            try:
                from services.exports.generate_pdf_bytes import PDFGenerator
                
                # Generate PDF bytes from the references markdown
                pdf_bytes, success_message = PDFGenerator.generate_pdf(
                    markdown_content=references_markdown,
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    cover_page_elements=None,
                    toc_data=None,
                    trailing_page_markdown=None,
                    compliance=None,
                    volume_number=volume_number,
                    image_only=False
                )
                
                logger.info(f"Successfully converted references to PDF: {success_message}")
                
            except ImportError as import_error:
                logger.error(f"Failed to import PDFGenerator: {import_error}")
            except Exception as pdf_error:
                logger.error(f"Failed to generate PDF for references: {pdf_error}")

            references_record_identifier = f"{opportunity_id}_references_vol_{volume_number}"
            
            # Save to datametastore
            async for db in get_customer_db():
                existing_record = await DataMetastoreController.get_by_record_identifier(
                    db, references_record_identifier
                )
                
                if existing_record:
                    await DataMetastoreController.update(
                        db=db,
                        record_id=existing_record.id,
                        original_document=pdf_bytes,
                        original_document_file_name=f"references_volume_{volume_number}_{opportunity_id}.pdf",
                        owner="SYSTEM"
                    )
                    logger.info(f"Updated existing references record for opportunity {opportunity_id}, volume {volume_number} with PDF")
                else:
                    new_record = await DataMetastoreController.add(
                        db=db,
                        record_identifier=references_record_identifier,
                        record_type="PROPOSAL_REFERENCE",
                        tenant_id=tenant_id,
                        original_document_content_type="application/pdf",
                        original_document_file_name=f"references_volume_{volume_number}_{opportunity_id}.pdf",
                        original_document=pdf_bytes,
                        # raw_text_document=references_markdown,
                        owner="SYSTEM"
                    )
                    if new_record:
                        logger.info(f"✓ Saved references as PDF for opportunity {opportunity_id}, volume {volume_number} to datametastore (ID: {new_record.id})")
                    else:
                        logger.error(f"Failed to save references PDF for opportunity {opportunity_id}, volume {volume_number}")
                break

        except Exception as e:
            logger.error(f"Error extracting and saving references as PDF for opportunity {opportunity_id}: {e}")
            import traceback
            traceback.print_exc()
    
    async def _generate_proposal_outline_custom(self, item: QueueItem):
        """Generate proposal outline for each TOC and store in CustomOppsTable"""
        logger.info(f"Generating Proposal Outline for opportunity: {item.opps_id}")

        try:
            opportunity_id = str(item.opps_id)
            # Retrieve all TOCs from DB
            async for db in get_customer_db():
                record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
                break
            if not record:
                row = None
                tenant_id = None
            else:
                row = (
                    record.toc_text,
                    record.toc_text_2,
                    record.toc_text_3,
                    record.toc_text_4,
                    record.toc_text_5,
                    
                )
                tenant_id = record.tenant_id
            if not row:
                logger.warning(f"No TOC found for opportunity: {item.opps_id}")
                return

            toc_fields = ["toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"]
            outline_fields = [
                "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
                "proposal_outline_4", "proposal_outline_5"
            ]
            update_fields = {}

            for idx, toc_text in enumerate(row):
                if toc_text is None:
                    continue
                try:
                    toc = ast.literal_eval(str(toc_text))
                except Exception:
                    logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for opportunity: {item.opps_id}")
                    continue

                table_of_contents = toc
                if not table_of_contents:
                    logger.warning(f"No table_of_contents in {toc_fields[idx]} for opportunity: {item.opps_id}")
                    continue

                # Generate the outline using the ProposalOutlineService
                outline_result = await self.proposal_outline_service.generate_outline_markdown(
                    opportunity_id=str(item.opps_id),
                    tenant_id=str(tenant_id),
                    source="custom",
                    table_of_contents=table_of_contents,
                    is_rfp=True
                )

                # Extract outline data
                outline_data = None
                if outline_result and "outlines" in outline_result:
                    outline_data = outline_result["outlines"]
                    logger.info(f"✓ Outline generated successfully with {len(outline_data)} sections for {toc_fields[idx]}")
                    
                    # Extract and save references immediately after outline generation
                    volume_number = idx + 1
                    await self._extract_and_save_references(
                        outline_result=outline_result,
                        opportunity_id=opportunity_id,
                        tenant_id=str(tenant_id),
                        volume_number=volume_number,
                        volume_toc=table_of_contents  # Pass the TOC for numbering
                    )
                else:
                    logger.warning(f"⚠ Outline generation failed for {toc_fields[idx]} in opportunity: {item.opps_id}")
                    continue

                # Store the outline result
                update_fields[outline_fields[idx]] = json.dumps(outline_result["outlines"], ensure_ascii=False)

            if update_fields:
                updated_record = None
                async for db in get_customer_db():
                    updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=opportunity_id,
                        update_fields=update_fields
                    )
                    break
                if updated_record:
                    logger.info(f"Successfully stored Proposal Outline for opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update CustomOppsTable with Proposal Outline for opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Proposal Outline for opportunity {item.opps_id}: {e}")
            raise

        
    async def _generate_proposal_outline_sam(self, item):
        """Generate proposal outline for each TOC and store in OppsTable"""
        logger.info(f"Generating Proposal Outline for SAM opportunity: {item.opps_id}")

        try:
            # Retrieve all TOCs from DB
            query = text(
                "SELECT toc_text, toc_text2, toc_text3, toc_text4, toc_text5 "
                "FROM kontratar_main.oppstable WHERE notice_id = :opps_id"
            )
            result = None
            async for db in get_kontratar_db():
                result = await db.execute(query, {"opps_id": item.opps_id})
                break
            row = result.first() if result else None
            if not row:
                logger.warning(f"No TOC found for SAM opportunity: {item.opps_id}")
                return

            toc_fields = ["toc_text", "toc_text2", "toc_text3", "toc_text4", "toc_text5"]
            outline_fields = [
                "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
                "proposal_outline_4", "proposal_outline_5"
            ]
            update_fields = {}

            for idx, toc_text in enumerate(row):
                if not toc_text:
                    continue
                try:
                    toc = ast.literal_eval(str(toc_text))
                except Exception:
                    logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for SAM opportunity: {item.opps_id}")
                    continue

                table_of_contents = toc
                if not table_of_contents:
                    logger.warning(f"No table_of_contents in {toc_fields[idx]} for SAM opportunity: {item.opps_id}")
                    continue
                logger.debug(f"Generating outline for TOC field {toc_fields[idx]}: {table_of_contents}")
                
                # Generate the outline using the ProposalOutlineService - same as pipeline_6.py
                outline_result = await self.proposal_outline_service.generate_outline_markdown(
                    opportunity_id=item.opps_id,
                    tenant_id=getattr(item, "tenant_id", None) or "default",
                    source="sam",
                    table_of_contents=table_of_contents,
                    is_rfp=True
                )

                # Extract outline data
                outline_data = None
                if outline_result and "outlines" in outline_result:
                    outline_data = outline_result["outlines"]
                    logger.info(f"✓ Outline generated successfully with {len(outline_data)} sections for {toc_fields[idx]}")
                    
                    # Extract and save references immediately after outline generation
                    volume_number = idx + 1
                    tenant_id = getattr(item, "tenant_id", None) or "default"
                    await self._extract_and_save_references(
                        outline_result=outline_result,
                        opportunity_id=item.opps_id,
                        tenant_id=tenant_id,
                        volume_number=volume_number,
                        volume_toc=table_of_contents  # Pass the TOC for numbering
                    )
                else:
                    logger.warning(f"⚠ Outline generation failed for {toc_fields[idx]} in SAM opportunity: {item.opps_id}")
                    continue

                # Store the outline result
                update_fields[outline_fields[idx]] = json.dumps(outline_result["outlines"], ensure_ascii=False)

            if update_fields:
                updated_record = None
                async for db in get_kontratar_db():
                    updated_record = await OppsController.update_by_opportunity_id(
                        db=db,
                        opportunity_id=item.opps_id,
                        update_fields=update_fields
                    )
                    break
                if updated_record:
                    logger.info(f"Successfully stored Proposal Outline for SAM opportunity: {item.opps_id}")
                else:
                    logger.error(f"Failed to update OppsTable with Proposal Outline for SAM opportunity: {item.opps_id}")

        except Exception as e:
            logger.error(f"Error generating Proposal Outline for SAM opportunity {item.opps_id}: {e}")
            raise

    def enable(self):
        self.is_enabled = True

    def disable(self):
        self.is_enabled = False

    def is_scheduler_enabled(self) -> bool:
        return self.is_enabled
    
    def start(self, interval_seconds: int = 30):
        if self.is_running:
            logger.warning("proposal outline scheduler is already running")
            return
        self.scheduler.add_job(
            self.process_outline_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_proposal_outline_queue",
            name="Process Proposal Outline Queue"
        )
        self.scheduler.start()
        self.is_running = True
        logger.info(f"Proposal Outline Queue scheduler started with {interval_seconds} second interval")

    def stop(self):
        if not self.is_running:
            logger.warning("Proposal Outline Queue scheduler is not running")
            return
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Proposal Outline Queue scheduler stopped")

    def restart(self, interval_seconds: int = 30):
        logger.info("Restarting Proposal Outline Queue scheduler...")
        self.stop()
        self.start(interval_seconds)
    
    def get_status(self) -> Dict[str, Any]:
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }
    
    def get_jobs(self) -> List[Dict[str, Any]]:
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs
  