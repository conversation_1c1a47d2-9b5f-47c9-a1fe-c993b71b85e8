import uuid
from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import ProposalQueue as CustomerProposalQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class ProposalQueueService:
    """Service for handling proposal queue operations"""
    
    @staticmethod
    async def get_proposal_queue_items(db: AsyncSession, limit: int = 10) -> List[CustomerProposalQueue]:
        """Get new proposal queue items"""
        try:
            query = select(CustomerProposalQueue).where(
                CustomerProposalQueue.status == "NEW" ##temporarily changed to NEW from N
            ).order_by(CustomerProposalQueue.creation_date.asc()).limit(limit)
            
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new proposal queue items: {e}")
            return []

    @staticmethod
    async def get_proposal_queue_item(db: AsyncSession, opportunity_id: str) -> List[CustomerProposalQueue]:
        """Get new proposal queue items"""
        try:
            query = select(CustomerProposalQueue).where(
                CustomerProposalQueue.opps_id == opportunity_id
            )
            
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting new proposal queue items: {e}")
            return []
    
    @staticmethod
    async def update_proposal_queue_status(
        db: AsyncSession, 
        job_id: str, 
        status: str, 
        next_state: Optional[str] = None
    ) -> bool:
        """Update proposal queue status"""
        try:
            query = update(CustomerProposalQueue).where(
                CustomerProposalQueue.job_id == job_id
            ).values(
                status=status,
                next_state=next_state
            )
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated proposal queue status for job_id {job_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating proposal queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def create_proposal_queue_item(
        db: AsyncSession,
        job_instruction: str,
        opps_id: str,
        tenant_id: str,
        request_type: int,
        job_submitted_by: Optional[str] = None,
        next_state: Optional[str] = None
    ) -> Optional[CustomerProposalQueue]:
        """Create a new proposal queue item"""
        try:
            job_id = str(uuid.uuid4())
            new_item = CustomerProposalQueue(
                job_id=job_id,
                job_instruction=job_instruction,
                job_submitted_by=job_submitted_by,
                status="NEW",
                next_state=next_state,
                opps_id=opps_id,
                tenant_id=tenant_id,
                request_type=request_type,
                creation_date=datetime.utcnow()
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new proposal queue item with job_id {job_id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating proposal queue item: {e}")
            await db.rollback()
            return None 