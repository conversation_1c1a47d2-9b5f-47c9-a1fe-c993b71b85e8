from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class TokenData(BaseModel):
    """Token data from the auth service response"""
    groupId: int
    tenantId: str
    id: int
    isActive: bool
    email: str
    sub: str
    iat: int
    exp: int


class AuthResponse(BaseModel):
    """Complete auth service response"""
    tokenData: TokenData
    groupId: int
    tenantId: str
    userEmail: str
    userName: str
    message: str
    userId: int


class CurrentUser(BaseModel):
    """Current authenticated user information"""
    user_id: int
    email: str
    name: str
    tenant_id: str
    group_id: int
    is_active: bool
    
    @classmethod
    def from_auth_response(cls, auth_response: AuthResponse) -> "CurrentUser":
        """Create CurrentUser from auth service response"""
        return cls(
            user_id=auth_response.userId,
            email=auth_response.userEmail,
            name=auth_response.userName,
            tenant_id=auth_response.tenantId,
            group_id=auth_response.groupId,
            is_active=auth_response.tokenData.isActive
        )


class AuthError(BaseModel):
    """Authentication error response"""
    error: str
    message: str
    status_code: int = 401
