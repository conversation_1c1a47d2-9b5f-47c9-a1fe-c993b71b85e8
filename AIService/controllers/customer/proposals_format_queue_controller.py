from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import ProposalsFormatQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class ProposalsFormatQueueController:
    """Controller for proposals format queue operations"""
    
    @staticmethod
    async def get_new_items(db: AsyncSession, limit: int = 10) -> List[ProposalsFormatQueue]:
        """Get new proposals format queue items with status NEW"""
        try:
            query = select(ProposalsFormatQueue).where(
                ProposalsFormatQueue.status == "NEW"
            ).order_by(ProposalsFormatQueue.created_date.asc()).limit(limit)
            
            result = await db.execute(query)
            return list(list(result.scalars().all()))
        except Exception as e:
            logger.error(f"Error getting new proposals format queue items: {e}")
            return []
    
    @staticmethod
    async def update_status(db: AsyncSession, queue_id: int, status: str) -> bool:
        """Update proposals format queue status"""
        try:
            query = update(ProposalsFormatQueue).where(
                ProposalsFormatQueue.id == queue_id
            ).values(status=status)
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated proposals format queue status for id {queue_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating proposals format queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def get_by_id(db: AsyncSession, queue_id: int) -> Optional[ProposalsFormatQueue]:
        """Get proposals format queue item by ID"""
        try:
            query = select(ProposalsFormatQueue).where(ProposalsFormatQueue.id == queue_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting proposals format queue item {queue_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_opps_id(db: AsyncSession, opps_id: str, limit: int = 100) -> List[ProposalsFormatQueue]:
        """Get proposals format queue items by opportunity ID"""
        try:
            query = select(ProposalsFormatQueue).where(
                ProposalsFormatQueue.opps_id == opps_id
            ).order_by(ProposalsFormatQueue.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposals format queue items for opps {opps_id}: {e}")
            return []
    
    @staticmethod
    async def get_by_tenant_id(db: AsyncSession, tenant_id: str, limit: int = 100) -> List[ProposalsFormatQueue]:
        """Get proposals format queue items by tenant ID"""
        try:
            query = select(ProposalsFormatQueue).where(
                ProposalsFormatQueue.tenant_id == tenant_id
            ).order_by(ProposalsFormatQueue.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposals format queue items for tenant {tenant_id}: {e}")
            return []
    
    @staticmethod
    async def get_current_version(db: AsyncSession, tenant_id: str, opps_id: str) -> Optional[int]:
        """
        Get the maximum version number for a proposals format queue item for a given tenant_id and opps_id.
        """
        try:
            from sqlalchemy import func
            query = select(func.max(ProposalsFormatQueue.version)).where(
                ProposalsFormatQueue.tenant_id == tenant_id,
                ProposalsFormatQueue.opps_id == opps_id
            )
            result = await db.execute(query)
            max_version = result.scalar_one()
            return max_version if max_version else 0
        except Exception as e:
            logger.error(f"Error getting max version for tenant {tenant_id} and opps {opps_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_status(db: AsyncSession, status: str, limit: int = 100) -> List[ProposalsFormatQueue]:
        """Get proposals format queue items by status"""
        try:
            query = select(ProposalsFormatQueue).where(
                ProposalsFormatQueue.status == status
            ).order_by(ProposalsFormatQueue.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting proposals format queue items with status {status}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[ProposalsFormatQueue]:
        """Get all proposals format queue items"""
        try:
            query = select(ProposalsFormatQueue).order_by(ProposalsFormatQueue.created_date.desc()).limit(limit)
            result = await db.execute(query)
            return list(result.scalars().all())
        except Exception as e:
            logger.error(f"Error getting all proposals format queue items: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        client_short_name: str,
        tenant_id: str,
        opps_id: str,
        version: int,
        format_type: int,
        proposal_data: bytes,
        job_submitted_by: str,
        is_rfp: bool,
        cover_page: int,
        opp_source: str,
        format_details: str,
        status: str 
    ) -> Optional[ProposalsFormatQueue]:
        """Add new proposals format queue item"""
        try:
            new_item = ProposalsFormatQueue(
                client_short_name=client_short_name,
                tenant_id=tenant_id,
                opps_id=opps_id,
                created_date=datetime.utcnow(),
                version=version,
                status=status,
                format_type=format_type,
                format_details=format_details,
                proposal_data=proposal_data,
                job_submitted_by=job_submitted_by,
                is_rfp=is_rfp,
                cover_page=cover_page,
                trailing_page=None,
                opp_source=opp_source
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new proposals format queue item {new_item.id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating proposals format queue item: {e}")
            await db.rollback()
            return None

    @staticmethod
    async def update(
        db: AsyncSession,
        queue_id: int,
        status: Optional[str] = None,
        proposal_data: Optional[bytes] = None,
        format_details: Optional[str] = None,
        job_submitted_by: Optional[str] = None,
        is_rfp: Optional[bool] = None,
        cover_page: Optional[int] = None,
        trailing_page: Optional[int] = None,
        opp_source: Optional[str] = None
    ) -> bool:
        """Update proposals format queue item"""
        try:
            item = await ProposalsFormatQueueController.get_by_id(db, queue_id)
            if not item:
                return False

            update_fields = {
                "status": status,
                "proposal_data": proposal_data,
                "format_details": format_details,
                "job_submitted_by": job_submitted_by,
                "is_rfp": is_rfp,
                "cover_page": cover_page,
                "trailing_page": trailing_page,
                "opp_source": opp_source
            }

            for field, value in update_fields.items():
                # Only update if value is not None
                if value is not None:
                    setattr(item, field, value)

            await db.commit()
            logger.info(f"Updated proposals format queue item {queue_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating proposals format queue item: {e}")
            await db.rollback()
            return False 