from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Optional, Any, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import json

from database import get_customer_db
from services.chat.enhanced_chat_service import EnhancedChatService
from schemas.chat_schemas import ChatRequest, ThreadCreateRequest
from services.chat.thread_service import ThreadService
from dependencies.auth import get_current_user
from schemas.auth_schemas import CurrentUser
from loguru import logger

# Create router with /chats prefix
router = APIRouter(prefix="/chats", tags=["chat"])

enhanced_chat_service = EnhancedChatService()


def _safe_serialize_metadata(metadata: Any) -> Dict[str, Any]:
    """Safely serialize metadata to avoid circular references"""
    if metadata is None:
        return {}

    if isinstance(metadata, dict):
        try:
            json.dumps(metadata)
            return metadata
        except (TypeError, ValueError):
            safe_metadata = {}
            for key, value in metadata.items():
                try:
                    json.dumps(value)
                    safe_metadata[str(key)] = value
                except (TypeError, ValueError):
                    safe_metadata[str(key)] = str(value)
            return safe_metadata

    try:
        return dict(metadata) if hasattr(metadata, '__dict__') else {}
    except:
        return {"raw_value": str(metadata)}

@router.post("/start")
async def start_new_chat(
    request: ChatRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Start a new chat and return thread info immediately.
    This endpoint creates a thread and returns the thread ID and title
    before starting the streaming response.
    """
    try:
        # Ensure user can only create chats for their tenant
        if request.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot create chats for other tenants"
            )

        logger.info(f"Starting new chat for opportunity: {request.opportunity_id} by user: {current_user.email}")

        # Validate message
        if not enhanced_chat_service.validate_message(request.message):
            raise HTTPException(status_code=400, detail="Invalid message format")

        # Force new thread creation by clearing thread_id
        request.thread_id = None

        # Get or create thread (this will create a new one)
        thread = await enhanced_chat_service._get_or_create_thread(
            db, request, request.tenant_id
        )

        return {
            "thread_id": str(thread.id),
            "title": thread.title,
            "opportunity_id": thread.opportunity_id,
            "tenant_id": thread.tenant_id,
            "source": thread.source,
            "created_date": thread.created_date.isoformat() if thread.created_date else None,
            "message": "Thread created successfully. Use /chats/ask to send messages."
        }

    except Exception as e:
        logger.error(f"Error starting new chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ask")
async def ask_question_stream(
    request: ChatRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Ask a question and get a Server-Sent Events streaming response.
    Creates a new thread or uses existing thread.
    """
    try:
        if request.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot ask questions for other tenants"
            )

        logger.info(f"Received SSE chat request: {request.opportunity_id} by user: {current_user.email}")

        # Validate message
        if not enhanced_chat_service.validate_message(request.message):
            raise HTTPException(status_code=400, detail="Invalid message format")

        async def generate_sse_response():
            """Generator for SSE formatted response"""
            try:
                # Send initial connection event
                yield enhanced_chat_service.format_sse_message("Connected", event="connection")
                
                if not request.thread_id:
                    thread = await enhanced_chat_service._get_or_create_thread(
                        db, request, request.tenant_id
                    )
                    thread_info = {
                        "thread_id": str(thread.id),
                        "title": thread.title,
                        "opportunity_id": thread.opportunity_id
                    }
                    yield enhanced_chat_service.format_sse_message(
                        json.dumps(thread_info), 
                        event="thread_created"
                    )
                
                # Send typing indicator
                yield enhanced_chat_service.format_sse_message("Assistant is typing...", event="typing")
                
                # Stream the actual chat response
                full_response = ""
                message_count = 0
                
                async for chunk in enhanced_chat_service.chat(
                    db=db,
                    request=request,
                    tenant_id=request.tenant_id,
                    streaming=True,
                    sse_format=True
                ):
                    if chunk and chunk.strip():
                        full_response += chunk
                        message_count += 1
                        yield chunk
                
                # Send completion event
                completion_data = {
                    "full_response": full_response,
                    "message_count": message_count,
                    "status": "completed"
                }
                yield enhanced_chat_service.format_sse_message(
                    json.dumps(completion_data), 
                    event="completed"
                )
                
            except Exception as e:
                logger.error(f"Error in SSE response generation: {e}")
                error_data = {"error": str(e), "status": "error"}
                yield enhanced_chat_service.format_sse_message(
                    json.dumps(error_data), 
                    event="error"
                )

        return StreamingResponse(
            generate_sse_response(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "Cache-Control",
            }
        )

    except Exception as e:
        logger.error(f"Error in SSE chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/threads")
async def list_threads(
    tenant_id: str,
    opportunity_id: Optional[str] = None,
    source: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    include_archived: bool = False,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """List chat threads for a tenant"""
    try:
        if tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot list threads for other tenants"
            )

        from schemas.chat_schemas import ThreadListRequest

        request = ThreadListRequest(
            limit=limit,
            offset=offset,
            include_archived=include_archived,
            opportunity_id=opportunity_id,
            source=source,
            tenant_id=tenant_id
        )

        threads = await ThreadService.list_threads(db=db, request=request)

        return [
            {
                "id": str(thread.id),
                "title": thread.title,
                "opportunity_id": thread.opportunity_id,
                "tenant_id": thread.tenant_id,
                "source": thread.source,
                "created_date": thread.created_date.isoformat() if thread.created_date else None,
                "last_activity_date": thread.last_activity_date.isoformat() if thread.last_activity_date else None,
                "is_archived": thread.is_archived,
                "message_count": thread.message_count,
                "summary": thread.summary
            }
            for thread in threads
        ]

    except Exception as e:
        logger.error(f"Error listing threads: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/threads/{thread_id}")
async def get_thread(
    thread_id: UUID,
    tenant_id: str,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """Get a specific thread with messages"""
    try:
        thread = await ThreadService.get_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id
        )

        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")

        messages = await ThreadService.get_thread_messages(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id
        )

        return {
            "id": str(thread.id),
            "title": thread.title,
            "opportunity_id": thread.opportunity_id,
            "tenant_id": thread.tenant_id,
            "source": thread.source,
            "created_date": thread.created_date.isoformat() if thread.created_date else None,
            "last_activity_date": thread.last_activity_date.isoformat() if thread.last_activity_date else None,
            "is_archived": thread.is_archived,
            "message_count": thread.message_count,
            "summary": thread.summary,
            "messages": [
                {
                    "id": str(msg.id),
                    "role": msg.role,
                    "content": msg.content,
                    "created_date": msg.created_date.isoformat() if msg.created_date else None,
                    "metadata": _safe_serialize_metadata(msg.metadata)
                }
                for msg in messages
            ]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/threads/{thread_id}")
async def delete_thread(
    thread_id: UUID,
    tenant_id: str,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """Delete a thread"""
    try:
        success = await ThreadService.delete_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id
        )

        if not success:
            raise HTTPException(status_code=404, detail="Thread not found")

        return {"success": True, "message": "Thread deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/threads/{thread_id}")
async def update_thread(
    thread_id: UUID,
    tenant_id: str,
    title: Optional[str] = None,
    is_archived: Optional[bool] = None,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """Update thread properties"""
    try:
        # Ensure user can only update threads for their tenant
        if tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: cannot update threads for other tenants"
            )

        from schemas.chat_schemas import ThreadUpdateRequest

        request = ThreadUpdateRequest(
            title=title,
            is_archived=is_archived
        )

        thread = await ThreadService.update_thread(
            db=db,
            thread_id=thread_id,
            tenant_id=tenant_id,
            request=request
        )

        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")

        logger.info(f"Thread updated by user: {current_user.email}")
        return {
            "success": True,
            "message": "Thread updated successfully",
            "thread_id": str(thread.id),
            "new_title": thread.title
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))