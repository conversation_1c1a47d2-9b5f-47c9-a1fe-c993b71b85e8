from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from database import get_customer_db
from services.proposal.rfp.generate_rfp import RFPGenerationService
from services.scheduler_service.proposal_scheduler_service import ProposalSchedulerService
from services.proposal.proposal_volumes_retrival import ProposalVolumeRetrievalService
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
from dependencies.auth import get_current_user
from schemas.auth_schemas import CurrentUser
from loguru import logger

outline_service = ProposalSchedulerService()

router = APIRouter(prefix="/proposals", tags=["proposal-export"])

class VolumeExportRequest(BaseModel):
    """Request model for exporting proposal volumes"""
    tenant_id: str = Field(..., description="Tenant ID")
    opportunity_id: str = Field(..., description="Opportunity ID")
    source: str = Field(..., description="Source type (sam, ebuy, custom)")
    client_short_name: str = Field(..., description="Client short name")
    cover_page: Optional[int] = Field(None, description="Cover page ID")
    export_type: int = Field(..., description="Export type (1=DOCX, 2=PDF)")
    job_submitted_by: str = Field(..., description="User who submitted the job")

class VolumeExportResponse(BaseModel):
    """Response model for volume export"""
    message: str
    opportunity_id: str
    tenant_id: str
    export_type: str
    volumes_processed: int

@router.post(
    "/export-volumes",
    response_model=VolumeExportResponse,
    summary="Export proposal volumes from database to PDF or DOCX format and mark as finalized"
)
async def export_proposal_volumes(
    request: VolumeExportRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: AsyncSession = Depends(get_customer_db)
):
    """
    Export proposal volumes to the specified format (PDF or DOCX).

    This endpoint:
    1. Fetches the latest version of section data from proposals_in_review table
    2. Decrypts and combines the data to create the all_volumes structure
    3. Exports the volumes to the specified format (PDF or DOCX)
    4. Marks all proposals as FINALIZED in the database
    """
    try:
        # Validate user access
        if request.tenant_id != current_user.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: cannot export volumes for other tenants"
            )

        logger.info(f"Exporting proposal volumes for opportunity {request.opportunity_id} by user: {current_user.email}")

        # Initialize services
        rfp_service = RFPGenerationService()
        proposal_service = ProposalSchedulerService()
        volume_retrieval_service = ProposalVolumeRetrievalService()

        # Fetch the latest version of section data from proposals_in_review table
        logger.info(f"Fetching latest proposal volumes from database for tenant {request.tenant_id}, opportunity {request.opportunity_id}")
        proposal_volumes = await volume_retrieval_service.get_all_volumes_from_review(
            db, request.tenant_id, request.opportunity_id
        )

        if not proposal_volumes:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No proposal volumes found for opportunity {request.opportunity_id}"
            )

        all_table_of_contents = await outline_service._get_all_table_of_contents(
            request.opportunity_id, request.tenant_id, request.source
        )
        
        logger.info(f"Converting all volumes to {request.export_type} format and saving to datametastore...")
        
        if request.export_type == 2:  # PDF
            await proposal_service._convert_volumes_to_pdf_bytes(
                proposal_volumes, 
                all_table_of_contents, 
                request.tenant_id, 
                request.opportunity_id, 
                request.source,
                request.client_short_name, 
                request.cover_page, 
                request.job_submitted_by
            )
            export_type_str = "PDF"
        elif request.export_type == 1:
            await proposal_service._convert_volumes_to_docx_bytes(
                proposal_volumes, 
                all_table_of_contents, 
                request.tenant_id, 
                request.opportunity_id, 
                request.source,
                request.client_short_name, 
                request.cover_page, 
                request.job_submitted_by
            )
            export_type_str = "DOCX"
        else:
            logger.warning(f"Unknown export type: {request.export_type}, defaulting to PDF")
            await proposal_service._convert_volumes_to_pdf_bytes(
                proposal_volumes, 
                all_table_of_contents, 
                request.tenant_id, 
                request.opportunity_id, 
                request.source,
                request.client_short_name, 
                request.cover_page, 
                request.job_submitted_by
            )
            export_type_str = "PDF"

        logger.info("Moving all volumes to format...")
        await rfp_service.move_all_volumes_to_format(
            proposal_volumes,
            request.tenant_id,
            request.opportunity_id,
            request.source,
            request.client_short_name,
            request.cover_page or 0,  # Handle None case
            request.export_type,
            request.job_submitted_by,
            "COMPLETED"
        )

        # Mark proposals as finalized in the database
        logger.info(f"Marking proposals as FINALIZED for tenant {request.tenant_id}, opportunity {request.opportunity_id}")
        finalized_success = await ProposalsInReviewController.update_finalized_status(
            db, request.tenant_id, request.opportunity_id, "FINALIZED"
        )

        if not finalized_success:
            logger.warning(f"Failed to update finalized status for tenant {request.tenant_id}, opportunity {request.opportunity_id}")

        volumes_processed = sum(1 for volume in proposal_volumes if volume is not None)

        logger.info(f"Successfully exported {volumes_processed} volumes to {export_type_str} format")
        
        return VolumeExportResponse(
            message=f"Successfully exported {volumes_processed} volumes to {export_type_str} format",
            opportunity_id=request.opportunity_id,
            tenant_id=request.tenant_id,
            export_type=export_type_str,
            volumes_processed=volumes_processed
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting proposal volumes for opportunity {request.opportunity_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error while exporting proposal volumes: {str(e)}"
        )
