import asyncio
import json
from services.scheduler_service.proposal_outline_scheduler_service import ProposalOutlineSchedulerService
from services.proposal.proposal_volumes_retrival import ProposalVolumeRetrievalService
from database import get_customer_db, CustomerSessionLocal
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import Depends
from services.scheduler_service.proposal_scheduler_service import ProposalSchedulerService
from services.scheduler_service.custom_opps_scheduler_service import CustomOppsSchedulerService
from loguru import logger
from services.proposal.structure_compliance import StructureComplianceService
from services.proposal.content_compliance import ContentComplianceService
from services.proposal.outline import ProposalOutlineService
import ast
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from typing import Optional, List, Dict, Any
from controllers.customer.datametastore_controller import DataMetastoreController
from controllers.customer.tenant_controller import TenantController
from services.proposal.document_title_extraction_service import DocumentTitleExtractionService


toc_data = {
  "opportunity_id": "78620e4931944f38b5edf68ca026ed93",
  "volume_number": 1,
  "volume_title": "Volume I - Business",
  "toc_raw_content": "{\n  \"table_of_contents\": [\n    {\n      \"title\": \"Cover Letter\",\n      \"description\": \"This section provides a formal introduction to our organization and our proposal. It highlights our understanding of the RFP requirements and summarizes our key qualifications and capabilities. It also expresses our enthusiasm for the opportunity and our commitment to delivering exceptional results. This section directly addresses the evaluation criteria related to overall proposal quality and responsiveness.\",\n      \"number\": \"A\",\n      \"page_limit\": 2,\n      \"subsections\": []\n    },\n    {\n      \"title\": \"Organizational Conflict of Interest Mitigation Plan\",\n      \"description\": \"This section details our approach to identifying and mitigating any potential organizational conflicts of interest (OCI) that may arise during the performance of this contract. It outlines our OCI policies and procedures, identifies any existing or potential OCIs, and describes the specific mitigation strategies we will implement to ensure impartiality and objectivity. This section directly addresses the evaluation criteria related to organizational integrity and compliance.\",\n      \"number\": \"B\",\n      \"page_limit\": 2,\n      \"subsections\": []\n    },\n    {\n      \"title\": \"Copy of GSA Schedule\",\n      \"description\": \"This section includes a copy of our GSA Schedule, demonstrating our pre-approved pricing and terms for the services offered under this RFP. It provides the government with readily available information on our contract vehicles and pricing structure, streamlining the acquisition process. This section directly addresses the evaluation criteria related to contract compliance and pricing.\",\n      \"number\": \"C\",\n      \"page_limit\": 1,\n      \"subsections\": []\n    }\n  ]\n}",
  "table_of_contents": [
    {
      "title": "Cover Letter",
      "description": "This section provides a formal introduction to our organization and our proposal. It highlights our understanding of the RFP requirements and summarizes our key qualifications and capabilities. It also expresses our enthusiasm for the opportunity and our commitment to delivering exceptional results. This section directly addresses the evaluation criteria related to overall proposal quality and responsiveness.",
      "number": "A",
      "page_limit": 2,
      "subsections": []
    },
    {
      "title": "Organizational Conflict of Interest Mitigation Plan",
      "description": "This section details our approach to identifying and mitigating any potential organizational conflicts of interest (OCI) that may arise during the performance of this contract. It outlines our OCI policies and procedures, identifies any existing or potential OCIs, and describes the specific mitigation strategies we will implement to ensure impartiality and objectivity. This section directly addresses the evaluation criteria related to organizational integrity and compliance.",
      "number": "B",
      "page_limit": 2,
      "subsections": []
    },
    {
      "title": "Copy of GSA Schedule",
      "description": "This section includes a copy of our GSA Schedule, demonstrating our pre-approved pricing and terms for the services offered under this RFP. It provides the government with readily available information on our contract vehicles and pricing structure, streamlining the acquisition process. This section directly addresses the evaluation criteria related to contract compliance and pricing.",
      "number": "C",
      "page_limit": 1,
      "subsections": []
    }
  ],
  "section_count": 3,
  "generated_at": "2025-08-19T22:23:44.938803"
}

outline_data = {"outlines": [{"title": "Executive Summary", "page_limit": 2, "markdown": "## Executive Summary (Page limit: 2 pages)\n\n**Purpose:** To provide a concise overview of the proposal, highlighting key aspects of the proposed approach and demonstrating understanding of the solicitation requirements.\n\n**Required Information:**\n\n*   Demonstrate understanding of all major evaluation criteria.\n*   Highlight key aspects of the proposed technical approach.\n*   Highlight key aspects of the proposed management approach.\n*   Summarize relevant past performance.\n*   Acknowledge and respond to the extended solicitation close date. (\"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\")\n*   Acknowledge previous changes to the response date. (\"The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00 PM.\")\n\n**Suggested Structure & Headings:**\n\n*   **Technical Approach Summary**\n    *   Briefly describe the proposed solution.\n    *   Highlight key features and benefits.\n*   **Management Approach Summary**\n    *   Outline the proposed project management methodology.\n    *   Describe the team’s qualifications and experience.\n*   **Relevant Past Performance**\n    *   Summarize relevant projects and accomplishments.\n    *   Demonstrate experience aligned with solicitation requirements.\n*   **Solicitation Updates & Compliance**\n    *   Confirm understanding of the extended solicitation close date. (\"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\")\n    *   Acknowledge previous date changes. (\"The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00 PM.\")\n\n**Required Tables / Diagrams:**\n\n*   None specified in provided context.\n\n**References:**\n\n*   \"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\"\n*   \"The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00 PM.\"", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["5 reference snippet(s) not quoted verbatim in markdown References block."]}, {"title": "Technical Approach", "page_limit": 8, "markdown": "## Technical Approach (Page limit: 8 pages)\n\n**Purpose:** To detail the proposed technical solution and demonstrate fulfillment of all solicitation requirements.\n\n**Required Information:**\n\n*   Address all aspects of the Statement of Work (SOW) tasks, providing specific details on how each task will be accomplished.\n*   Demonstrate compliance with all technical specifications and standards.\n*   Include relevant diagrams, charts, and other visual aids to enhance clarity and understanding.\n*   Acknowledge the extended solicitation close date of July 28, 2025. (\"The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.\")\n\n**Suggested Structure & Headings:**\n\n*   **1. Overview of Proposed Solution**\n    *   High-level description of the technical approach.\n    *   Key technologies and methodologies to be employed.\n*   **2. Detailed Task Breakdown & Implementation**\n    *   For each task outlined in the Statement of Work (SOW):\n        *   Detailed description of the proposed implementation approach.\n        *   Specific technologies and tools to be utilized.\n        *   Deliverables for each task.\n        *   Timeline for completion.\n*   **3. Technical Compliance & Standards**\n    *   Demonstration of compliance with all technical specifications and standards outlined in the solicitation.\n    *   Mapping of proposed solution to specific requirements.\n*   **4. Visual Aids & Supporting Documentation**\n    *   Diagrams illustrating system architecture and data flow.\n    *   Charts depicting project timelines and resource allocation.\n    *   Supporting documentation (e.g., data sheets, specifications).\n*   **5. Risk Assessment & Mitigation**\n    *   Identification of potential technical risks.\n    *   Proposed mitigation strategies.\n\n**Required Tables / Diagrams:**\n\n*   **Task Breakdown Table:** A table listing each SOW task, the proposed implementation approach, deliverables, and timeline.\n*   **Compliance Matrix:** A matrix mapping each technical requirement to the corresponding aspect of the proposed solution.\n*   **System Architecture Diagram:** A visual representation of the proposed system architecture, including key components and data flow.\n*   **Project Timeline Chart:** A Gantt chart or similar visualization depicting the project timeline and key milestones.\n\n**References:**\n\n*   “Address all aspects of the Statement of Work (SOW) tasks, providing specific details on how each task will be accomplished.”\n*   “Demonstrate compliance with all technical specifications and standards.”\n*   “Include relevant diagrams, charts, and other visual aids to enhance clarity and understanding.”\n*   “The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00 PM.”\n*   OpportunitySAMTable [id=209922, title=1355-01-387-7738 T107 Suspension Band Set MK89 MOD1, description=https://api.sam.gov/prod/opportunities/v1/noticedesc?noticeid=78620e4931944f38b5edf68ca026ed93, postedDate=2025-06-25 00:00:00.0, archiveDate=2025-08-12 00:00:00.0, naicsCode=325920, naicsCodes=325920, typeOfSetAside=, typeOfSetAsideDescription=N/A, noticeId=78620e4931944f38b5edf68ca026ed93, solicitationNumber=N0010425RK010, fullParentPathName=DEPT OF DEFENSE.DEPT OF THE NAVY.NAVSUP.NAVSUP WEAPON SYSTEMS SUPPORT.NAVSUP WSS MECHANICSBURG.NAVSUP WEAPON SYSTEMS SUPPORT MECH, fullParentPathCode=017.1700.NAVSUP.NAVSUP WSS.NAVSUP WSS MECH.N00104, typeOp=Solicitation, baseTypeOp=Solicitation, archiveType=auto15, classificationCode=1355, pointOfContactName=Telephone: ************, pointOfContactEmail=<EMAIL>, pointOfContactPhone=N/A, placeOfPerformanceCityName=null, placeOfPerformanceStateName=null, placeOfPerformanceZip=null, placeOfPerformanceCountryName=null, ulink=https://sam.gov/opp/78620e4931944f38b5edf68ca026ed93/view, createdDate=2025-06-25 07:46:04.328, descriptionText=null, agencyCode=017, lastModDate=null, responseDeadLine=2025-07-28 14:00:00.0, pointOfContactFax=null, pointOfContactType=null, pointOfContactTitle=null, officeAddressZipcode=null, officeAddressCity=null, officeAddressCountryCode=null, officeAddressState=null, active=Yes, awardDate=null, awardNumber=null, awardAmount=null, awardAwardeeName=null, awardAwardeeLocationStreetAddress=null, awardAwardeeLocationCityCode=null, awardAwardeeLocationCityName=null, awardAwardeeLocationStateCode=null, awardAwardeeLocationStateName=null, awardAwardeeLocationZip=null, awardAwardeeLocationCountryCode=null, awardAwardeeLocationCountryName=null, awardAwardeeUeiSam=null, awardAwardeeDuns=null, awardAwardeeCageCode=null, additionalInfoLink=null, pointOfContactFullName=null, organizationType=null, placeOfPerformanceCityCode=null, placeOfPerformanceStateCode=null, placeOfPerformanceCountryCode=null, summaryText=null, requirementText=null, gradingCriteriaText=null, tocText=null, tocText2=null, tocText3=null, tocText4=null, tocText5=null, formatCompliance=null, keywords=null, status=null]", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["Forbidden token found in markdown: TO BE", "5 reference snippet(s) not quoted verbatim in markdown References block."]}, {"title": "Management Approach", "page_limit": 5, "markdown": "## Management Approach (Page limit: 5 pages)\n\n**Purpose:** To detail the project management plan, demonstrating a clear understanding of best practices and the ability to successfully manage the project.\n\n**Required Information:**\n\n*   Demonstrate understanding of project management best practices.\n*   Detail the organizational structure.\n*   Define roles and responsibilities.\n*   Outline communication strategies.\n*   Describe risk management procedures.\n*   Detail quality assurance processes.\n*   Address all relevant management requirements specified in the solicitation.\n\n**Suggested Structure & Headings:**\n\n*   **Project Organization (1 page)**\n    *   Organizational Chart: Visual representation of the project team structure.\n    *   Roles and Responsibilities: Detailed descriptions of each team member’s duties and authorities.\n*   **Communication Plan (1 page)**\n    *   Communication Matrix:  Details frequency, method, and audience for key project communications.\n    *   Reporting Procedures:  Describes how progress will be tracked and reported.\n*   **Risk Management (1.5 pages)**\n    *   Risk Identification:  List of potential risks and their likelihood/impact.\n    *   Mitigation Strategies:  Plans to reduce the probability or impact of identified risks.\n    *   Contingency Planning:  Actions to be taken if risks materialize.\n*   **Quality Assurance (1 page)**\n    *   Quality Control Measures:  Processes to ensure deliverables meet specified requirements.\n    *   Performance Metrics:  Key indicators used to track project performance.\n    *   Issue Resolution Process:  How issues will be identified, tracked, and resolved.\n*   **Project Schedule & Tracking (0.5 page)**\n    *   High-level project timeline.\n    *   Methods for tracking progress against the schedule.\n\n**Required Tables / Diagrams:**\n\n*   **Organizational Chart:**  A visual representation of the project team structure, showing reporting relationships.\n*   **Communication Matrix:** A table detailing communication methods, frequency, audience, and responsible parties.\n*   **Risk Register:** A table listing identified risks, their likelihood, impact, mitigation strategies, and contingency plans.\n\n**References:**\n\n*   “Describe the project management plan, including organizational structure, roles and responsibilities, communication strategies, risk management procedures, and quality assurance processes.”\n*   “This section should demonstrate a clear understanding of project management best practices and the ability to effectively manage the project to successful completion.”\n*   “Address all relevant management requirements specified in the solicitation.”", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["Forbidden token found in markdown: TO BE", "5 reference snippet(s) not quoted verbatim in markdown References block."]}, {"title": "Past Performance", "page_limit": 5, "markdown": "## Past Performance (Page limit: 5 pages)\n\n**Purpose:** To demonstrate the offeror’s capabilities and experience in successfully completing similar projects.\n\n**Required Information:**\n\n*   Description of three (3) relevant past projects.\n*   For each project:\n    *   Project description.\n    *   Offeror’s role.\n    *   Results achieved.\n    *   Challenges overcome.\n*   Quantifiable results.\n*   Client testimonials (highly encouraged).\n*   Demonstration of ability to meet the requirements of this solicitation.\n\n**Suggested Structure & Headings:**\n\n*   **Project 1: [Project Name]**\n    *   Project Overview: Detailed description of the project.\n    *   Offeror’s Role: Specific responsibilities and contributions.\n    *   Results Achieved: Quantifiable outcomes and benefits delivered.\n    *   Challenges Overcome: Description of obstacles and solutions implemented.\n*   **Project 2: [Project Name]**\n    *   Project Overview\n    *   Offeror’s Role\n    *   Results Achieved\n    *   Challenges Overcome\n*   **Project 3: [Project Name]**\n    *   Project Overview\n    *   Offeror’s Role\n    *   Results Achieved\n    *   Challenges Overcome\n\n**Required Tables / Diagrams:**\n\n*   None explicitly required. Tables summarizing project results or challenges could be beneficial but are not mandated.\n\n**References:**\n\n*   “Description of three (3) relevant past projects that demonstrate the offeror's capabilities and experience in successfully completing similar projects. For each project, include a description of the project, the offeror's role, the results achieved, and any challenges overcome.”\n*   “Quantifiable results and client testimonials are highly encouraged.”\n*   “This section should clearly demonstrate the offeror's ability to meet the requirements of this solicitation.”", "references": ["N3598A24NA045 AMENDMENT OF SOLICITATION/MODIFICATION OF CONTRACT Except as provided herein, all terms and conditions of the document referenced in Item 9A or 10A, as heretofore changed, remains unchanged and in full force and effect. 15A. NAME AND TITLE OF SIGNER...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0004 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 26-Jun-2025 02:00 PM to 28-Jul-2025 02:00...", "ACCOUNTING AND APPROPRIATION DATA (If required) 13. THIS ITEM APPLIES ONLY TO MODIFICATIONS OF CONTRACTS/ORDERS. IT MODIFIES THE CONTRACT/ORDER NO. AS DESCRIBED IN ITEM 14. A. THIS CHANGE ORDER IS ISSUED PURSUANT TO: (Specify authority) THE CHANGES SET FORTH IN...", "REQUISITION/PURCHASE REQ. NO. CODE See Item 6 FACILITY CODECODE EMAIL:TEL: N0010425RK010 0003 SECTION SF 30 BLOCK 14 CONTINUATION PAGE SUMMARY OF CHANGES SECTION A - SOLICITATION/CONTRACT FORM The required response date/time has changed from 16-Jun-2025 02:00 PM to 26-Jun-2025 02:00..."], "validation_warnings": ["5 reference snippet(s) not quoted verbatim in markdown References block."]}], "generation_summary": {"total_sections": 4, "produced": 4, "success_rate": 100.0}}
tenant_id = ""


extractor = ProposalOutlineSchedulerService()
retrieval = ProposalVolumeRetrievalService()
outline = ProposalSchedulerService()
compliance = CustomOppsSchedulerService()

structure_compliance_service = StructureComplianceService()
content_compliance_service = ContentComplianceService()
proposal_outline_service = ProposalOutlineService()

document_title_extraction_service = DocumentTitleExtractionService()

opportunity_id = toc_data["opportunity_id"]
volume_number = toc_data["volume_number"]
volume_toc = toc_data["table_of_contents"]

opportunity_id = "vSe1unlCj9"

tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
client = "adeptengineeringsolutions"
source = "custom"


async def generate_structure_compliance(opps_id, tenant_id, source):

  """Generate structure compliance and store in CustomOppsTable"""
  logger.info(f"Generating structure compliance for opportunity: {opps_id}")
  
  try:
      # Generate structure compliance using the service
      structure_result = await structure_compliance_service.generate_structure_compliance(
          opportunity_id=str(opps_id),
          tenant_id=str(tenant_id),
          source=str(source) or "custom",  # Default to "custom" if source is None
          max_tokens=2048
      )
      
      # Extract the generated content
      structure_data = structure_result.get("structured_data", {})
      structure_content_str = str(structure_data)
      update_fields = {
          "structure_compliance": structure_content_str
      }
      async for db in get_customer_db():
          updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
              db=db,
              opportunity_id=str(opportunity_id),
              update_fields=update_fields
          )
          break
      
      if updated_record:
          logger.info(f"Successfully stored structure compliance for opportunity: {opportunity_id}")
      else:
          logger.error(f"Failed to update CustomOppsTable for opportunity: {opportunity_id}")
      return structure_data
          
  except Exception as e:
      logger.error(f"Error generating structure compliance for opportunity {opps_id}: {e}")
      # Don't re-raise the exception to allow other processing steps to continue
  
  
async def generate_content_compliance(opps_id, tenant_id, source):
  """Generate content compliance and store in CustomOppsTable"""
  logger.info(f"Generating content compliance for opportunity: {opps_id}")
  
  try:
      
      content_result = await content_compliance_service.generate_content_compliance(
          opportunity_id=str(opps_id),
          tenant_id=str(tenant_id),
          source=str(source) or "custom",  # Default to "custom" if source is None
          is_rfp=True,
      )
      
      # Extract the generated content
      content_compliance_content = content_result.get("structured_data", {})

      content_compliance_str = str(content_compliance_content)
      
      # Update the CustomOppsTable with the content compliance
      update_fields = {
          "content_compliance": content_compliance_str
      }
      async for db in get_customer_db():
          updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
              db=db,
              opportunity_id=str(opportunity_id),
              update_fields=update_fields
          )
          break
      
      if updated_record:
          logger.info(f"Successfully stored content compliance for opportunity: {opportunity_id}")
      else:
          logger.error(f"Failed to update CustomOppsTable for opportunity: {opportunity_id}")
      
      return content_compliance_content
          
  except Exception as e:
      logger.error(f"Error generating content compliance for opportunity {opps_id}: {e}")
      # Don't re-raise the exception to allow other processing steps to continue


async def generate_table_of_contents(structure_compliance_str, content_compliance_str, opps_id, source):
  """Generate Table of Contents for each volume and store in CustomOppsTable"""
  logger.info(f"Generating Table of Contents for opportunity: {opps_id}")

  try:
      # Parse structure compliance JSON
      structure_compliance = ast.literal_eval(str(structure_compliance_str))
      content_compliance = ast.literal_eval(str(content_compliance_str))
      if not structure_compliance or "structure" not in structure_compliance:
          logger.warning(f"Invalid structure compliance JSON for opportunity: {opps_id}")
          return
      
      volume_definitions = structure_compliance["structure"]
      print(volume_definitions)

      # Split compliance data by volume
      volumes = {}

      # Extract structure data
      for volume in volume_definitions:
          volume_title = volume.get("volume_title", "")
          volumes[volume_title] = {
              "structure": volume,
              "content": None
          }

      # Match content compliance to volumes
      if content_compliance and "content_compliance" in content_compliance:
          logger.info("Using structured content compliance data...")
          for compliance in content_compliance["content_compliance"]:
              volume_title = compliance.get("volume_title", "")
              # Match volume titles
              for vol_key in volumes.keys():
                  if volume_title.lower() in vol_key.lower() or vol_key.lower() in volume_title.lower():
                      volumes[vol_key]["content"] = compliance
                      break

      else:
          logger.warning("No structured content compliance, will use full content for each volume")
          # Use full content compliance for each volume
          for vol_key in volumes.keys():
              volumes[vol_key]["content"] = {"content": content_compliance}

      logger.info(f"✓ Split compliance into {len(volumes)} volumes: {list(volumes.keys())}")

      update_fields = {}
      toc_fields = [
          "toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"
      ]

      print(f"VOLUMES: {volumes}")

      # Process each volume through the complete pipeline
      for volume_title, volume_data in volumes.items():
          logger.info(f"PROCESSING VOLUME: {volume_title}")

          # Prepare volume-specific data
          volume_structure = json.dumps({"structure": [volume_data["structure"]]}, indent=2)
          volume_content = json.dumps({"content_compliance": [volume_data["content"]]}, indent=2) if volume_data["content"] else "{}"

          # Generate TOC for this volume
          toc_result = await proposal_outline_service.generate_table_of_contents(
              opportunity_id=opportunity_id,
              tenant_id=tenant_id,
              source=source or "custom",
              volume_information=volume_structure,
              content_compliance=volume_content,
              is_rfp=True
          )
          print(f"TOC: {toc_result}")
          # Extract TOC JSON
          toc_data = None
          if toc_result and "content" in toc_result:
              content = toc_result["content"]
              try:
                  # Look for JSON in the content
                  import re
                  json_match = re.search(r'\{.*\}', content, re.DOTALL)
                  if json_match:
                      json_str = json_match.group()
                      toc_data = json.loads(json_str)
              except:
                  pass
          print("TOC_DATA: {toc_data}")
          if toc_data and "table_of_contents" in toc_data:
              volume_toc = toc_data["table_of_contents"]
              logger.info(f"✓ TOC generated successfully with {len(volume_toc)} sections")
          else:
              logger.error("⚠ TOC generation failed or no valid JSON found")
              volume_toc = []

          volume_number = list(volumes.keys()).index(volume_title)
          update_fields[toc_fields[volume_number]] = volume_toc

      if update_fields:
        async for db in get_customer_db():
            updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                db=db,
                opportunity_id=opportunity_id,
                update_fields=update_fields
            )
            break
        if updated_record:
            logger.info(f"Successfully stored Table of Contents for opportunity: {opportunity_id}")
        else:
            logger.error(f"Failed to update CustomOppsTable with TOC for opportunity: {opportunity_id}")

      return update_fields
      
  except Exception as e:
    logger.error(f"Error generating Table of Contents for opportunity {opps_id}: {e}")



async def extract_and_save_references(outline_result: dict, opportunity_id: str, tenant_id: str, volume_number: int = 1, volume_toc: Optional[list] = None):
    
    """Extract references from outline data and save to datametastore database as PDF bytes with TOC numbering"""
    try:
        if not outline_result or "outlines" not in outline_result:
            logger.info(f"No outline data found for opportunity {opportunity_id}, skipping references extraction")
            return

        toc_numbering = {}
        if volume_toc:
            for toc_item in volume_toc:
                title = toc_item.get("title", "")
                number = toc_item.get("number", "")
                if title and number:
                    toc_numbering[title] = number

        all_references = []
        for section in outline_result["outlines"]:
            title = section.get("title", "")
            references = section.get("references", [])

            # Get section number from TOC or use default
            section_number = toc_numbering.get(title, "")

            # Collect references for separate storage
            if references:
                section_refs = {
                    "section_number": section_number,
                    "section_title": title,
                    "references": references
                }
                all_references.append(section_refs)

        if not all_references:
            logger.info(f"No references found for opportunity {opportunity_id}")
            return

        references_content_lines = []
        references_content_lines.append(f"# References - Volume {volume_number}")
        references_content_lines.append("")
        
        for section_refs in all_references:
            section_number = section_refs.get("section_number", "")
            section_title = section_refs.get("section_title", "")
            references = section_refs.get("references", [])
            
            if references:
                header_title = f"{section_number} {section_title}" if section_number else section_title
                references_content_lines.append(f"## {header_title} - References")
                references_content_lines.append("")
                
                for i, reference in enumerate(references, 1):
                    cleaned_ref = reference.strip().replace('"', '').replace("'", "'")
                    references_content_lines.append(f"{i}. {cleaned_ref}")
                
                references_content_lines.append("")

        # Join all content into markdown
        references_markdown = "\n".join(references_content_lines)

        print(references_markdown)
        
        # Convert markdown to PDF bytes using PDFGenerator
        try:
            from services.exports.generate_pdf_bytes import PDFGenerator
            
            # Generate PDF bytes from the references markdown
            pdf_bytes, success_message = PDFGenerator.generate_pdf(
                markdown_content=references_markdown,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                cover_page_elements=None,
                toc_data=None,
                trailing_page_markdown=None,
                compliance=None,
                volume_number=volume_number,
                image_only=False
            )

            print(f"PDF_BYTES: {pdf_bytes}")
            
            logger.info(f"Successfully converted references to PDF: {success_message}")
            
        except ImportError as import_error:
            logger.error(f"Failed to import PDFGenerator: {import_error}")
        except Exception as pdf_error:
            logger.error(f"Failed to generate PDF for references: {pdf_error}")

        references_record_identifier = f"{opportunity_id}_references_vol_{volume_number}"
        
        # Save to datametastore
        async for db in get_customer_db():
            existing_record = await DataMetastoreController.get_by_record_identifier(
                db, references_record_identifier
            )
            
            if existing_record:
                await DataMetastoreController.update(
                    db=db,
                    record_id=existing_record.id,
                    original_document=pdf_bytes,
                    original_document_file_name=f"references_volume_{volume_number}_{opportunity_id}.pdf",
                    owner="SYSTEM"
                )
                logger.info(f"Updated existing references record for opportunity {opportunity_id}, volume {volume_number} with PDF")
            else:
                new_record = await DataMetastoreController.add(
                    db=db,
                    record_identifier=references_record_identifier,
                    record_type="PROPOSAL_REFERENCE",
                    tenant_id=tenant_id,
                    original_document_content_type="application/pdf",
                    original_document_file_name=f"references_volume_{volume_number}_{opportunity_id}.pdf",
                    original_document=pdf_bytes,
                    # raw_text_document=references_markdown,
                    owner="SYSTEM"
                )
                if new_record:
                    logger.info(f"✓ Saved references as PDF for opportunity {opportunity_id}, volume {volume_number} to datametastore (ID: {new_record.id})")
                else:
                    logger.error(f"Failed to save references PDF for opportunity {opportunity_id}, volume {volume_number}")
            break

    except Exception as e:
        logger.error(f"Error extracting and saving references as PDF for opportunity {opportunity_id}: {e}")
        import traceback
        traceback.print_exc()


async def generate_proposal_outline_custom(opportunity_id, tenant_id):
  """Generate proposal outline for each TOC and store in CustomOppsTable"""
  logger.info(f"Generating Proposal Outline for opportunity: {opportunity_id}")

  try:
      # Retrieve all TOCs from DB
      async for db in get_customer_db():
          record = await CustomOpportunitiesController.get_by_opportunity_id(db, opportunity_id)
          break
      if not record:
          row = None
      else:
          row = (
              record.toc_text,
              record.toc_text_2,
              record.toc_text_3,
              record.toc_text_4,
              record.toc_text_5,
          )
      if not row:
          logger.warning(f"No TOC found for opportunity: {opportunity_id}")
          return

      toc_fields = ["toc_text", "toc_text_2", "toc_text_3", "toc_text_4", "toc_text_5"]
      outline_fields = [
          "proposal_outline_1", "proposal_outline_2", "proposal_outline_3",
          "proposal_outline_4", "proposal_outline_5"
      ]
      update_fields = {}

      for idx, toc_text in enumerate(row):
          if toc_text is None:
              continue
          try:
              toc = ast.literal_eval(str(toc_text))
          except Exception:
              logger.warning(f"TOC field {toc_fields[idx]} is not valid JSON for opportunity: {opportunity_id}")
              continue

          table_of_contents = toc
          if not table_of_contents:
              logger.warning(f"No table_of_contents in {toc_fields[idx]} for opportunity: {opportunity_id}")
              continue

          # Generate the outline using the ProposalOutlineService
          outline_result = await proposal_outline_service.generate_outline_markdown(
              opportunity_id=opportunity_id,
              tenant_id=tenant_id,
              source="custom",
              table_of_contents=table_of_contents,
              is_rfp=True
          )

          # Extract outline data
          outline_data = None
          if outline_result and "outlines" in outline_result:
              outline_data = outline_result["outlines"]
              logger.info(f"✓ Outline generated successfully with {len(outline_data)} sections for {toc_fields[idx]}")
              
              # Extract and save references immediately after outline generation
              volume_number = idx + 1
              await extract_and_save_references(
                  outline_result=outline_result,
                  opportunity_id=opportunity_id,
                  tenant_id=tenant_id,
                  volume_number=volume_number,
                  volume_toc=table_of_contents
              )
          else:
              logger.warning(f"⚠ Outline generation failed for {toc_fields[idx]} in opportunity: {opportunity_id}")
              continue

          # Store the outline result
          update_fields[outline_fields[idx]] = json.dumps(outline_result["outlines"], ensure_ascii=False)

      if update_fields:
          updated_record = None
          async for db in get_customer_db():
              updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                  db=db,
                  opportunity_id=opportunity_id,
                  update_fields=update_fields
              )
              break
          if updated_record:
              logger.info(f"Successfully stored Proposal Outline for opportunity: {opportunity_id}")
          else:
              logger.error(f"Failed to update CustomOppsTable with Proposal Outline for opportunity: {opportunity_id}")
      return update_fields
  except Exception as e:
      logger.error(f"Error generating Proposal Outline for opportunity {opportunity_id}: {e}")
      raise
  
async def generate_all_volumes(all_table_of_contents, source):
    proposal_volumes: List[List[Dict[str, Any]] | None] = []
    tenant_metadata = ""
    async for db in get_customer_db():
        tenant = await TenantController.get_by_tenant_id(db, tenant_id)
        tenant_metadata = f"{tenant}" if tenant else ""
        break


    for idx, table_of_contents in enumerate(all_table_of_contents):
        current_volume = idx + 1
        logger.info(f"Processing volume {current_volume}")
        requested_volumes = [1, 2, 3, 4, 5]
        # Skip current volume if it is not part of requested volumes
        if current_volume not in requested_volumes:
            logger.info(f"Skipping volume {current_volume} as it is not in requested_volumes")
            proposal_volumes.append(None)
            continue

        # Skip if no table of contents available
        if table_of_contents is None or len(table_of_contents) == 0:
            logger.warning(f"No table of contents for volume {current_volume}, skipping")
            proposal_volumes.append(None)
            continue

        # Generate draft for this volume using outline_service.generate_draft
        logger.info(f"Generating draft for volume {current_volume} using outline service")
        draft_result = await proposal_outline_service.generate_draft(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name=client,
            tenant_metadata=tenant_metadata,
            table_of_contents=table_of_contents
        )

        # Extract draft data
        draft_data = None
        if draft_result and "draft" in draft_result:
            draft_data = draft_result["draft"]
            logger.info(f"✓ Draft generated successfully with {len(draft_data)} sections for volume {current_volume}")
            proposal_volumes.append(draft_data)
        else:
            logger.warning(f"⚠ Draft generation failed for volume {current_volume}")
            proposal_volumes.append(None)

    return proposal_volumes

async def generate_submission_requirements(opportunity_id, source):
    """Generate submission requirements and store in CustomOppsTable"""
    logger.info(f"Generating submission requirements for opportunity: {opportunity_id}")

    try:
        # Get opportunity data from database
        async for db in get_customer_db():
            opportunity_record = await CustomOpportunitiesController.get_by_opportunity_id(
                db, str(opportunity_id)
            )
            break

        if not opportunity_record:
            logger.warning(f"No opportunity record found for opportunity: {opportunity_id}")
            return

        # Extract submission requirements using the service
        submission_requirements = await document_title_extraction_service.extract_submission_requirements(
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source or "custom"
        )

        if not submission_requirements:
            logger.warning(f"No submission requirements generated for opportunity: {opportunity_id}")
            return

        # Convert to JSON string for storage
        submission_requirements_json = json.dumps(submission_requirements, ensure_ascii=False)

        # Update the CustomOppsTable with the submission requirements
        update_fields = {
            "submission_requirements": submission_requirements_json
        }

        async for db in get_customer_db():
            updated_record = await CustomOpportunitiesController.update_by_opportunity_id(
                db=db,
                opportunity_id=opportunity_id,
                update_fields=update_fields
            )
            break

        if updated_record:
            logger.info(f"Successfully stored submission requirements for opportunity: {opportunity_id}")
        else:
            logger.error(f"Failed to update CustomOppsTable with submission requirements for opportunity: {opportunity_id}")
        
        return submission_requirements
    
    except Exception as e:
        logger.error(f"Error generating submission requirements for opportunity {opportunity_id}: {e}")
        # Don't re-raise the exception to allow other processing steps to continue



# pdf_bytes = b'%PDF-1.4\n%\x93\x8c\x8b\x9e ReportLab Generated PDF document http://www.reportlab.com\n1 0 obj\n<<\n/F1 2 0 R /F2 3 0 R /F3 4 0 R\n>>\nendobj\n2 0 obj\n<<\n/BaseFont /Helvetica /Encoding /WinAnsiEncoding /Name /F1 /Subtype /Type1 /Type /Font\n>>\nendobj\n3 0 obj\n<<\n/BaseFont /Times-Bold /Encoding /WinAnsiEncoding /Name /F2 /Subtype /Type1 /Type /Font\n>>\nendobj\n4 0 obj\n<<\n/BaseFont /Times-Roman /Encoding /WinAnsiEncoding /Name /F3 /Subtype /Type1 /Type /Font\n>>\nendobj\n5 0 obj\n<<\n/Contents 10 0 R /MediaBox [ 0 0 612 792 ] /Parent 9 0 R /Resources <<\n/Font 1 0 R /ProcSet [ /PDF /Text /ImageB /ImageC /ImageI ]\n>> /Rotate 0 /Trans <<\n\n>> \n  /Type /Page\n>>\nendobj\n6 0 obj\n<<\n/Contents 11 0 R /MediaBox [ 0 0 612 792 ] /Parent 9 0 R /Resources <<\n/Font 1 0 R /ProcSet [ /PDF /Text /ImageB /ImageC /ImageI ]\n>> /Rotate 0 /Trans <<\n\n>> \n  /Type /Page\n>>\nendobj\n7 0 obj\n<<\n/PageMode /UseNone /Pages 9 0 R /Type /Catalog\n>>\nendobj\n8 0 obj\n<<\n/Author (\\(anonymous\\)) /CreationDate (D:20250822021550+01\'00\') /Creator (\\(unspecified\\)) /Keywords () /ModDate (D:20250822021550+01\'00\') /Producer (ReportLab PDF Library - www.reportlab.com) \n  /Subject (\\(unspecified\\)) /Title (\\(anonymous\\)) /Trapped /False\n>>\nendobj\n9 0 obj\n<<\n/Count 2 /Kids [ 5 0 R 6 0 R ] /Type /Pages\n>>\nendobj\n10 0 obj\n<<\n/Filter [ /ASCII85Decode /FlateDecode ] /Length 1770\n>>\nstream\nGat=*gMZ"A&:Ml+eD"S9d\':hYD_-uUUi6W`\'!7JMW3I<*=kcZ^gL9[I>L`q01Gal"KS6!OFfN`d9DdZ+a*6E\\#4P+c=#W><!CU_mS#cqD/W?E^V[NNDZ4fL8e\\f;7k.*@_\\\\N"[a\\iA<%\'9YWT<&/Zfsk:C[Ep+YKn29IW3jVK"J6h5!n$IiE/1&8MVnAX\'VBY)f.rZf#):!8R1e:*p3V=l<q%/<1FNhS7s;Is=SJ3779=JC:[l^Q%S,",r<.$7$YT\\pn:S:_j4maB`)`nnJ5n:-UIRoq=9_&1"tIfLN[7KKH\'R;J"$BB)erP9,"QQg`X6]7ATq<W.MV"Gr.BRO!"g/$M4B?d+1q+Z[eI1Rc$a@:^^B811q#f(i>7"Sf8D7%m*:OO./3hZ7W0^`f/C:s._bn%RdV+II$]HB$8-<m,q_,0D*8&>FOeWSo0%(H8phC]%mgr9QVa;5)@uS)K8$hCW]Gs-,]lp=0Et/E`i[$pabDQ)/H7G-obUo2kPt=?%iLYrW;).3DINZVr6nHrQQkQIQ_d6d`1%K+V/Ze!+_@GmSRgrC<Q0!!:G*Wg!#6ZQ!<XOb>&m6R!@;g.,=/4led%s3YBt%tm/i7U>OeJcrO%TEW+o3_1D%q-l(nt\\6!38,[@UF9qEIp$+/P+nQri)KCcKPsR?\\#HD(4T94<dc6Ns/mbemsCe5G/lVu@)mj)@._g\\^$eJOfsG\'-bU/P$@PI6-_L+\'Wh@H=lqU6UXXbW[j_g!jBhpt*FGF"S^e(Fg+?JnbTA$!Nm&U@8b<,5HhK9R0ASA@G]Zm_4=@0QArj:.X:Bfii2e#*O8ZZq,@;[/n2d,/o8TJl_0Lp,U#.E1+,57(AU=:8\'\'QC?K1.h$$J$6#1=IljGTL.@-\\3NObV=6UmYQ2X%i7cP;<$s9\\HP\\Jubk7J63`8m4(CJ2V#>fOjD3,LI]UV\\fu`%,i__W!$R"]m.dC0>]X7PcI9fp/X%1p\\E6`3\\cSArBZH>Y#<O,u\\MbL`GbECB?(ZH7G^CMoG@kPcn>9"H6`L2Ccd#;qc]$KKcD0G$^lATH)c)"FJm9aJ0qR+BRcsb]YFXi8%FTbX9%#P#:Imdmu)u1ZULdJVZVX(I[mu1FT/$MnqU[jbj(ZKRL`8bJ%R;I+BK!=,ak%YB4f-PfN/dPsm$-4G7Kpm.l)i$sIk\'heV/79hEf+1ee`B>@#"6<ema-314tq>;CI]c4d;1%CWnc(lm_jb\\eR"CBXUTjDR.@iEqZsW]*gl1(H\\<(iKts]eUnSdkL(EhR]S9]jhs4qL`HDrR:EOf2%E$EONciBT&CDKEAoCn>rRgE\\&kV>&Ilr;lnp;:RDs!<19p;8*)W#M[ap%)RZ4RGg5Pd"*PQ@L=,<%3`s]+O(@pqn%O0u3;M97ZW$S82M69).+;XZR4$,sY?sZ7j0_l=B(Yt$)@Q:1_Wf$-*(S/_-<RG/F:p^min5;/a%6C+:qW3D%1JOfcNY,31Rkn^BLgDl,*[-7L,\\mRC\\F8mGiTf0D`/SuaB.&`c`U8Jq2Pn,,H\'/FlcT[B@Y#a-P9Tp\\G8p`Mc$P(c)r7)e[\\s\\Rs5t%7MrN6.E2&;"P]&6N8\'5Of7+J/D[QI9f@Hhtsa@D-6./i;-d:tVQZD;_R<N\\)WQl9/3-e^_p<+31uNQ53?"5tN,_/?;M6gW#GSeW,,N=SeglCMn+n=2Vj,1Zq*o*T/hH![tu4?SVEdL.>;aA%\\;5f7Ii2d;tAi;>,04P2^&>RXKX@GJF)-iNW3EO8f`46Qb_rrWnN[gW~>endstream\nendobj\n11 0 obj\n<<\n/Filter [ /ASCII85Decode /FlateDecode ] /Length 1025\n>>\nstream\nGasam95iiK&AI=/bb6HO_VpB<U.9)nCj->!\'A$8D.,YJ#O"E8QW0#N?lEBYIG.+G0bn^?`MA56#b_(O<&+AD]"LuS3_h%u_6FI+?YQ1sjQtI3g(]B1$EHdMKn@\\5_2pJ$Xk3#iFmu#(U#Z4,i8f3!-78kB]Qe-2WXo#=X8g:?qY-euJCe$Dpo*KNNW]IpC]u.]^i(cCKZQ5)Mq]2u73ik+>me,A4mkF5oT3]jVLS\'#/iYN6uqGS(q81\\=DHZ\')X+RJOjO=*-L%WWi[\\kl9aV(8L(Q=qi_?eH8od#c``LhasI)rU]/R@fUsJ3DpcO*NjE3-g!9KDnU)PpqZ$7S\\<hV,%TH:bkYpUu0jc[poWAPhPKFdA!1^bE[dV3$@S7Td`uaidXor!L7iTM/\'k@U>>tNJS"4Tl$FE[`il*r#eT1hlEY:U$IE.6D@s_C6\'tqD:4V3=0ip.!(+r+8i/[XROUs)UOiF@erJ<TKYT6*B>XPqp(Q7DB!-64KD]8A+[h&5dqVgj[^>;,$5D."%RXL^0iSeB)?[qKe0B%BUo>_8?l#6&9Cjs\'id=OQb@C[IJ*OWKS:2[64pT)/XX"65\'U,1PW_$\\FnRo33bk`=F[_o<"<-!$%h[<gVim"I@LO>]j8]$u]c)dnZR1obV?KB<P!M5@>@7N\'A.5X`D4El(GF;dYc)YMg(qK9]I?0iJ:QdtT!W1o.`]=kfEAQ\'0DuS28eg>pEl?6JWLD6p,$Sf`=uPMt&XoOrj"?aJ/<M<?sZ[#i\\)@fiNT$92DS)h(V>hj`7@h$:F(iEcoI>b$=8l4/iU^i4[VDe$i4FZQb:>K_NjKA#*[\'73-?WAIGqY&4R_uNr2g_h4PGk\\Q.\'+UNq$#KhZ@)#Z=f^JsNha!FJV-i]%<nmed"*:nnOjPU.5_c^]A-cYB.s+<2(D#iUPQ`\'PP\\_T6k0Lmp<[S;)%1T,eN961%I7WNjFh:n"`-Zp7^eV!LZTALU]7H:I2rQUV[(hh&)q5)$0\\:@i=`II2j-rnDe0p^:nK_lE~>endstream\nendobj\nxref\n0 12\n0000000000 65535 f \n0000000073 00000 n \n0000000124 00000 n \n0000000231 00000 n \n0000000339 00000 n \n0000000448 00000 n \n0000000642 00000 n \n0000000836 00000 n \n0000000904 00000 n \n0000001187 00000 n \n0000001252 00000 n \n0000003114 00000 n \ntrailer\n<<\n/ID \n[<12fbcc25ac404d9fe4c23e01c146269e><12fbcc25ac404d9fe4c23e01c146269e>]\n% ReportLab generated PDF document -- digest (http://www.reportlab.com)\n\n/Info 8 0 R\n/Root 7 0 R\n/Size 12\n>>\nstartxref\n4231\n%%EOF\n'


# structure_compliance = str({'structure': [{'volume_title': 'Technical Capability', 'content': [{'section_name': 'Technical Capability', 'page_limit': 10}], 'total_page_limit': 10}, {'volume_title': 'Price', 'content': [{'section_name': 'Price', 'page_limit': 1}], 'total_page_limit': None}]})
# content_compliance = str({'content_compliance': [{'volume_title': 'Volume I – Technical Capability', 'content': "This volume shall be clear, concise, and include sufficient detail for effective evaluation and substantiating claims. It must be legible, clear, and coherent.  The offer should not simply rephrase or restate the Government's requirements but provide convincing rationale demonstrating how the offeror intends to meet them. Unacceptable statements include those simply claiming understanding, compliance, or paraphrasing of the PWS, or using generic phrases like “standard procedures will be employed.”  Offerors must assume the Government has no prior knowledge of their facilities and experience. The evaluation will be based solely on the information presented in the offer. Content must be consistent with the PWS and evaluation criteria in 52.212-2 Addendum.  It should be formatted logically and with enough detail for a thorough evaluation of the contractor’s technical competence and ability to comply with the contract task requirements in the PWS.", 'page_limit': 10, 'evaluation_criteria': ['Technical Capability factors defined in 52.212-2 Addendum'], 'mandatory_sections': ['Response to PWS requirements (See attached Performance Work Statement)', 'Response to 52.212-2 Evaluation Factors (See attached Addendum 52.212-2)']}, {'volume_title': 'Volume II – Price', 'content': 'Complete Section B of the solicitation. The electronic version will take precedence for any discrepancies between hard copy and electronic versions. Certified cost or pricing data is not anticipated. Complete blocks 30a, b, and c. List Unit Pricing and Total Pricing for each Line Item (See attached Performance Work Statement). Complete the specified blocks on the SF 1449 related to Wage Determination (See attached Wage Determination), Addendum 52.212-1 Instructions to Offerors (See attached Addendum 52.212-1), and Addendum 52.212-2 Evaluation Factors (See attached Addendum 52.212-2).', 'page_limit': None, 'evaluation_criteria': [], 'mandatory_sections': None}]})
# all_volumes = [[{'title': '1.0 Technical Capability', 'content': "Our approach to autonomous AI-driven outreach and personalized engagement for up to 32,500 students leverages a multi-faceted strategy incorporating advanced machine learning algorithms, natural language processing (NLP), and real-time analytics.  This solution directly addresses the USMA's need for increased efficiency, improved lead quality, and enhanced engagement with potential candidates.\n\n**1. AI-Powered Lead Generation and Qualification:**\n\n*   **Predictive Modeling:** We utilize proprietary algorithms trained on historical recruitment data and publicly available information to identify and prioritize high-potential candidates. This model considers factors such as academic performance, extracurricular activities, demonstrated leadership qualities, and geographic location to generate highly refined prospect lists.  This directly addresses potential inefficiencies in traditional recruiting methods and maximizes recruiter productivity.\n*   **Automated Outreach:** Our platform automates personalized outreach across multiple channels (email, SMS, social media) using NLP to tailor messaging based on individual candidate profiles.  This ensures consistent and engaging communication while freeing up recruiters to focus on high-value interactions.\n*   **Lead Scoring and Prioritization:**  A dynamic lead scoring system, based on engagement metrics and predicted conversion probability, prioritizes leads for recruiter follow-up, ensuring efficient allocation of resources and improved conversion rates.\n\n**2. Personalized Engagement and Support:**\n\n*   **Interactive Chatbot:**  An AI-powered chatbot provides 24/7 support to potential candidates, answering frequently asked questions, providing application guidance, and scheduling consultations with recruiters. This enhances candidate experience and reduces the burden on human recruiters.\n*   **Personalized Content Recommendations:**  Our platform analyzes candidate interactions and preferences to recommend relevant content, such as videos, testimonials, and blog posts, fostering deeper engagement and building stronger connections with the USMA brand.\n*   **Automated Follow-up and Nurturing:**  The system automates personalized follow-up communications based on candidate engagement, ensuring consistent and timely interaction throughout the recruitment lifecycle.\n\n**3. Seamless Integration and Real-Time Reporting:**\n\n*   **Slate CRM Integration:**  Our solution seamlessly integrates with USMA's existing Slate CRM system, ensuring data consistency and eliminating manual data entry. This integration facilitates efficient data management and reporting.\n*   **Real-Time Dashboards:**  Interactive dashboards provide real-time visibility into key performance indicators (KPIs), including outreach effectiveness, lead conversion rates, and campaign performance. This enables data-driven decision-making and continuous optimization of recruitment strategies.\n*   **Technical Support and Training:**  We provide comprehensive technical support and training to USMA personnel, ensuring smooth platform adoption and maximizing the value of our solution.\n\n**4. Performance Standards and Metrics:**\n\n| Metric                     | Target                                   | Measurement Method                               | Reporting Frequency |\n| -------------------------- | ----------------------------------------- | ------------------------------------------------ | ------------------ |\n| Platform Uptime            | 99.9%                                    | System monitoring and automated alerts            | Monthly             |\n| Lead Generation            | 7,500 inquiries                           | Tracked within the platform and Slate CRM        | Weekly              |\n| Conversion Rate (Inquiry to Application) | 25%                                      | Tracked within the platform and Slate CRM        | Monthly             |\n| Recruiter Productivity     | 20% increase in qualified leads per recruiter | Comparison to baseline data prior to implementation | Quarterly           |\n\n\nOur proposed solution directly addresses the challenges faced by USMA by streamlining recruitment processes, improving lead quality, and enhancing candidate engagement.  By leveraging advanced AI technologies and focusing on personalized interactions, we are confident in our ability to exceed the performance standards outlined in the PWS and deliver measurable results for the USMA.", 'number': '1.0', 'is_cover_letter': False, 'content_length': 4467, 'validation_passed': True, 'subsections': [{'title': '1.1 Technical Approach', 'content': "Our approach centers on a multi-pronged strategy leveraging AI-driven outreach, personalized engagement, and seamless integration with USMA's Slate CRM to achieve the desired outcomes.  This strategy addresses the core challenge of efficiently reaching and engaging a large target audience while maintaining personalized communication and minimizing the burden on USMA staff.\n\n**Autonomous AI-Driven Outreach:**\n\n*   **Targeted Prospecting:** Our proprietary AI algorithms analyze multiple data sources (e.g., publicly available data, educational databases, social media presence) to identify and prioritize potential candidates based on USMA's ideal applicant profile. This goes beyond simple demographic filtering and incorporates predictive modeling to identify individuals with a high likelihood of interest and qualification, addressing the challenge of inefficient traditional recruiting methods.\n*   **Personalized Messaging:**  Each outreach communication is dynamically generated based on the individual prospect's profile, interests, and engagement history. This personalized approach fosters a stronger connection with potential recruits and increases engagement rates, directly addressing the need for more effective communication strategies.\n*   **Automated Communication Flows:**  Our platform automates multi-channel outreach (email, SMS, social media) based on pre-defined workflows, ensuring consistent and timely communication while minimizing manual effort. This addresses the need for increased efficiency and reduced workload on USMA recruiters.\n\n**Personalized Engagement and Support:**\n\n*   **Interactive Chatbot:**  An AI-powered chatbot provides instant answers to common inquiries, freeing up USMA staff to focus on more complex interactions. The chatbot is trained on USMA-specific information and continuously learns from user interactions, ensuring accurate and relevant responses. This addresses the need for 24/7 support and improved response times.\n*   **Personalized Content Recommendations:**  Based on individual prospect profiles and engagement history, our platform recommends relevant content (e.g., videos, articles, testimonials) to further nurture interest and provide valuable information. This addresses the need for targeted and engaging content that resonates with potential recruits.\n*   **Automated Scheduling and Reminders:**  The platform automates scheduling for information sessions, interviews, and other events, reducing administrative overhead and ensuring timely follow-up. This addresses the need for streamlined processes and improved communication efficiency.\n\n**Seamless Integration with USMA's Slate CRM:**\n\n*   **Real-Time Data Synchronization:**  Our platform integrates directly with Slate CRM, ensuring that all prospect data, interactions, and progress are automatically synchronized in real-time. This eliminates manual data entry, reduces errors, and provides a single source of truth for all recruiting activities. This addresses the need for data integrity and efficient data management.\n*   **Customizable Reporting and Dashboards:**  We provide real-time dashboards and customizable reports that track key performance indicators (KPIs) such as outreach effectiveness, conversion rates, and applicant demographics. This provides USMA with actionable insights to optimize recruiting strategies and measure the success of the program. This addresses the need for data-driven decision-making and performance monitoring.\n\n**Technical Support and Training:**\n\n*   **Dedicated Support Team:**  We provide a dedicated support team available via phone, email, and online chat to address any technical issues or questions. This ensures that USMA staff have the support they need to effectively utilize the platform.\n*   **Comprehensive Training Program:**  We offer a comprehensive training program that covers all aspects of the platform, from basic navigation to advanced features. This ensures that USMA staff are fully equipped to leverage the platform's capabilities.\n\n**Performance Standards and Metrics:**\n\n| Metric                     | Target                               | Measurement Method                                     |\n| -------------------------- | ------------------------------------ | ----------------------------------------------------- |\n| Platform Uptime            | 99.9%                               | Automated system monitoring                           |\n| Outreach Volume            | 32,500 students reached             | Platform usage logs                                    |\n| Inquiry Generation         | 7,500 inquiries generated            | Platform tracking of inquiries and lead generation     |\n| Conversion Rate (Inquiry to Application) | 25%                                  | Tracking of application submissions through Slate CRM |\n| Reporting Frequency        | Weekly and monthly reports            | Automated report generation                           |\n| Training Completion Rate   | 100% for designated USMA personnel | Online training platform tracking                      |\n\n\nThis approach, combining AI-driven automation with personalized engagement and seamless integration, directly addresses USMA's need for a more efficient, effective, and data-driven recruiting strategy.  It tackles the root causes of recruiting challenges by optimizing prospect identification, improving communication effectiveness, and streamlining administrative processes.", 'number': '1.1', 'is_cover_letter': False, 'content_length': 5475, 'validation_passed': True}, {'title': '1.2 Past Performance/Demonstrated Experience', 'content': '**Past Performance/Demonstrated Experience**\n\n**1. Project Title:** Veteran Integration Program Enhancement\n**Client:** Department of Veterans Affairs\n**Contract Number:** VA-1234567890\n**Period of Performance:** 2021-2024\n\nThis project involved developing and implementing an AI-powered platform to connect veterans with relevant employment opportunities and support services. Our role encompassed the design, development, and integration of the AI engine, as well as the development of personalized outreach campaigns.  The platform leveraged natural language processing (NLP) to analyze veteran resumes and match them with suitable job postings.  We also integrated the platform with existing VA databases to provide personalized recommendations for support services based on individual veteran profiles.  Key outcomes included a 15% increase in veteran employment rates within six months of program enrollment and a 20% increase in veteran utilization of support services.\n\n**2. Project Title:**  Targeted Outreach for STEM Talent\n**Client:** National Science Foundation\n**Contract Number:** NSF-9876543210\n**Period of Performance:** 2022-2025\n\nThis project focused on developing a sophisticated outreach program to identify and engage high-potential STEM students for scholarship opportunities. We implemented a multi-faceted approach that included AI-driven social media campaigns, personalized email marketing, and targeted virtual events.  Our AI algorithms analyzed student data from various sources, including academic records and online profiles, to identify individuals with a high likelihood of pursuing STEM careers.  This targeted approach resulted in a 30% increase in scholarship applications and a 25% increase in the diversity of the applicant pool.\n\n**3. Project Title:**  Modernizing Workforce Recruitment\n**Client:** Department of Homeland Security\n**Contract Number:** DHS-0102030405\n**Period of Performance:** 2020-2023\n\nWe partnered with DHS to modernize their workforce recruitment processes by implementing a cloud-based CRM system integrated with AI-driven candidate screening tools.  Our team configured the CRM to automate key recruitment workflows, including application processing, interview scheduling, and onboarding.  The AI tools analyzed candidate resumes and application materials to identify top talent based on specific job requirements.  This streamlined approach reduced the time-to-hire by 40% and improved the overall quality of hires, as measured by performance evaluations in the first year of employment.  Furthermore, the integrated performance reporting dashboards provided real-time insights into key recruitment metrics, enabling data-driven decision-making.', 'number': '1.2', 'is_cover_letter': False, 'content_length': 2709, 'validation_passed': True}, {'title': '1.3 Management and Staffing', 'content': "Adept Engineering Solutions proposes a streamlined management structure designed for efficient communication and rapid response to USMA's evolving recruitment needs.  Our approach emphasizes collaboration and proactive problem-solving to ensure seamless integration of our Autonomous AI Recruiter Services.\n\n**Project Manager:** Fortune Alebiosu will serve as the primary point of contact for USMA, overseeing all project activities and ensuring adherence to performance metrics and deadlines.  Mr. Alebiosu brings over 10 years of experience managing complex technical projects for government clients, including successful implementations of AI-driven solutions.  His expertise in agile methodologies and risk management will ensure the project stays on track and within budget.\n\n**AI Development Lead:**  Dr. Anya Sharma will lead the AI development team, responsible for the design, development, and deployment of the autonomous AI recruiter. Dr. Sharma holds a PhD in Computer Science with a specialization in Machine Learning and has extensive experience developing and deploying AI solutions for talent acquisition.  Her expertise will ensure the AI recruiter is optimized for USMA's specific requirements, including targeting high-potential candidates and streamlining the application process.\n\n**Data Scientist:**  David Chen will be responsible for data analysis, model training, and ongoing performance optimization of the AI recruiter. Mr. Chen holds a Master's degree in Data Science and has a proven track record of developing predictive models for talent acquisition.  He will work closely with Dr. Sharma to ensure the AI recruiter continuously learns and improves its effectiveness.\n\n**Integration Specialist:**  Maria Rodriguez will manage the seamless integration of the AI recruiter with USMA's existing systems and processes.  Ms. Rodriguez has over 5 years of experience integrating complex software solutions for government clients.  Her expertise will minimize disruption and ensure a smooth transition to the new AI-powered recruiting system.\n\n**Communication and Reporting:**\n\n*   **Weekly Status Meetings:**  We will conduct weekly status meetings with USMA representatives to provide updates on project progress, address any challenges, and discuss upcoming activities.\n*   **Monthly Performance Reports:** We will deliver comprehensive monthly performance reports detailing key metrics such as the number of qualified candidates identified, application processing time, and overall recruiting efficiency improvements.\n*   **Dedicated Communication Channels:** We will establish dedicated communication channels (email, phone, and video conferencing) to ensure prompt and efficient communication between our team and USMA personnel.\n\n**Quality Control:**\n\nWe are committed to delivering high-quality services that meet USMA's stringent requirements.  Our quality control process includes:\n\n*   **Code Reviews:** All AI code will undergo rigorous peer reviews to ensure code quality, security, and adherence to best practices.\n*   **Automated Testing:** We will implement automated testing procedures to identify and address any potential issues early in the development cycle.\n*   **Performance Monitoring:** We will continuously monitor the performance of the AI recruiter and make adjustments as needed to optimize its effectiveness.\n\n\n| Role                | Name             | Experience (Years) | Key Skills                                     |\n|---------------------|-----------------|-------------------|-------------------------------------------------|\n| Project Manager      | Fortune Alebiosu | 10+               | Agile Methodologies, Risk Management, AI Solutions |\n| AI Development Lead | Anya Sharma      | 8+                | Machine Learning, AI Development, Talent Acquisition |\n| Data Scientist      | David Chen       | 5+                | Predictive Modeling, Data Analysis, AI Optimization |\n| Integration Specialist | Maria Rodriguez  | 5+                | System Integration, Software Deployment          |\n\n\nThis team's combined expertise in AI development, data science, and project management, coupled with our robust communication and quality control processes, will ensure the successful implementation and operation of the Autonomous AI Recruiter Services, ultimately helping USMA achieve its recruitment goals and address the challenges of attracting top talent in a competitive landscape.  Resumes for key personnel are included in Appendix A.", 'number': '1.3', 'is_cover_letter': False, 'content_length': 4503, 'validation_passed': True}]}], [{'title': '2.0 Price', 'content': 'This price proposal details all costs associated with fulfilling the Performance Work Statement (PWS).  Pricing is structured for clarity and reflects our commitment to delivering exceptional value to the government.\n\n### Summary of Costs\n\n| Task Area | Description | Unit of Measure | Unit Price | Quantity | Total Price |\n|---|---|---|---|---|---|\n| Project Management | Overall project oversight, reporting, and communication | Hour | \\$150 | 200 | \\$30,000 |\n| Systems Analysis & Design | Requirements analysis, system design, and documentation | Hour | \\$125 | 300 | \\$37,500 |\n| Software Development | Coding, testing, and integration of software components | Hour | \\$100 | 500 | \\$50,000 |\n| Data Migration & Integration | Data extraction, transformation, and loading into the new system | Hour | \\$85 | 100 | \\$8,500 |\n| Training & Documentation | User training materials and system documentation development | Hour | \\$75 | 50 | \\$3,750 |\n| System Deployment & Support | System deployment, post-implementation support, and maintenance | Hour | \\$110 | 150 | \\$16,500 |\n| **Total Project Cost** |  |  |  |  | **\\$146,250** |\n\n\n### Cost Breakdown by Labor Category\n\n| Labor Category | Hourly Rate | Total Hours | Total Cost |\n|---|---|---|---|\n| Senior Systems Engineer | \\$175 | 100 | \\$17,500 |\n| Software Developer | \\$120 | 400 | \\$48,000 |\n| Data Analyst | \\$95 | 75 | \\$7,125 |\n| Technical Writer | \\$80 | 50 | \\$4,000 |\n| Project Manager | \\$150 | 200 | \\$30,000 |\n| Systems Administrator | \\$110 | 100 | \\$11,000 |\n| Training Specialist | \\$75 | 50 | \\$3,750 |\n| **Total Labor Costs** |  |  | **\\$121,375** |\n\n### Other Direct Costs (ODCs)\n\n| ODC Item | Description | Total Cost |\n|---|---|---|\n| Software Licenses | Required software licenses for development and deployment | \\$5,000 |\n| Hardware | Server and network equipment | \\$10,000 |\n| Travel | Travel expenses for project team members | \\$4,875 |\n| Training Materials | Printing and distribution of training materials | \\$500 |\n| **Total ODCs** |  | **\\$19,875** |\n\n### Indirect Costs (G&A)\n\nIndirect costs are calculated at a rate of 10% of total direct costs (labor + ODCs).\n\n| Cost Category | Rate | Basis | Total Cost |\n|---|---|---|---|\n| G&A | 10% | \\$141,250 | \\$14,125 |\n\n### Assumptions and Basis of Estimate\n\n*   All rates are fully loaded and inclusive of fringe benefits, overhead, and profit.\n*   Travel costs are estimated based on current per diem rates and anticipated travel requirements.\n*   Hardware and software costs are based on current market prices and specific project needs.\n*   This pricing is valid for 90 days from the date of submission.\n\n\n### Payment Terms\n\nPayment will be invoiced monthly based on actual hours worked and deliverables completed.  Net 30 payment terms apply.', 'number': '2.0', 'is_cover_letter': False, 'content_length': 2782, 'validation_passed': True}]]
# all_toc = [[{'title': 'Technical Capability', 'description': "This section demonstrates our technical capability to meet all requirements of the Performance Work Statement (PWS) and the evaluation criteria outlined in 52.212-2 Addendum.  It provides a comprehensive overview of our approach to providing autonomous AI-driven outreach, personalized engagements, support for both vendor-generated and USMA-supplied leads, communications with up to 32,500 students, generating up to 7,500 inquiries, delivering real-time dashboards, providing technical support, ensuring integration with USMA’s Slate CRM, and meeting performance standards related to reporting, conversion rates, platform uptime, and training. This section will detail our understanding of the government's requirements and present a convincing rationale for our proposed solution, avoiding generic phrases and ensuring clarity and coherence for effective evaluation.", 'number': '1.0', 'page_limit': 10, 'subsections': [{'number': '1.1', 'title': 'Technical Approach', 'description': "This subsection details our technical approach to fulfilling each task outlined in the PWS.  It describes the specific methodologies, technologies, and processes we will employ to achieve the desired outcomes, including autonomous AI-driven outreach, personalized engagements, support for both vendor-generated and USMA-supplied leads, communications with up to 32,500 students, generating up to 7,500 inquiries, delivering real-time dashboards, providing technical support, ensuring integration with USMA’s Slate CRM, and meeting performance standards related to reporting, conversion rates, platform uptime, and training.  This includes a detailed explanation of our AI algorithms, data management strategies, and integration plan for USMA's Slate CRM.", 'page_limit': 2}, {'number': '1.2', 'title': 'Past Performance/Demonstrated Experience', 'description': "This subsection presents three relevant past performance examples demonstrating our successful experience in delivering similar solutions. Each example includes the project name, client, contract number, period of performance, and a concise description of the project's scope, our role, and the achieved outcomes. These examples showcase our proven ability to meet or exceed the requirements outlined in the PWS, specifically in areas such as AI-driven outreach, personalized engagements, CRM integration, and performance reporting.", 'page_limit': 2}, {'number': '1.3', 'title': 'Management and Staffing', 'description': 'This subsection details our proposed management structure and staffing plan for this project. It identifies key personnel, their roles and responsibilities, and their relevant experience.  It also describes our organizational structure, communication protocols, and quality control processes to ensure effective project management and successful execution of all PWS tasks. This includes resumes of key personnel demonstrating their qualifications and experience in relevant areas.', 'page_limit': 2}]}], [{'title': 'Price', 'description': 'This section provides a comprehensive price breakdown in accordance with Section B of the solicitation.  It includes unit pricing and total pricing for each line item as detailed in the attached Performance Work Statement (PWS). This section adheres to the Wage Determination and ADDENDUMs 52.212-1 and 52.212-2, referenced on the SF 1449.  The electronic version of the solicitation takes precedence in case of discrepancies. Certified cost or pricing data is not anticipated. Blocks 30a, b, and c are completed as required. All instructions and evaluation factors outlined in the solicitation are addressed within this price proposal.', 'number': '2.0', 'page_limit': 2, 'subsections': []}]]

async def main():
    # await extractor._extract_and_save_references(
    #     outline_result=outline_data,
    #     opportunity_id=opportunity_id,
    #     tenant_id=tenant_id,
    #     volume_number=volume_number,
    #     volume_toc=volume_toc
    # )
    async with CustomerSessionLocal() as db:
        all_volumes = await retrieval.get_all_volumes_from_review(
            db=db,
            tenant_id=tenant_id,
            opportunity_id=opportunity_id
        )
    print(all_volumes)

    # all_outlines = await outline._get_all_outlines(
    #     opportunity_id, tenant_id, "custom"
    # )

    # # print(all_outlines)

    # all_toc = await outline._get_all_table_of_contents(
    #     opportunity_id, tenant_id, "custom"
    # )

    # print(all_toc)


    # all_volumes = await generate_all_volumes(
    #     all_toc, "custom"
    # )

    # print(all_volumes)

    # await outline._convert_volumes_to_pdf_bytes(
    #     all_volumes, all_toc, tenant_id, opportunity_id, "custom", client, 4344, "SYSTEM"
    # )

    # await outline._convert_volumes_to_docx_bytes(
    #     all_volumes, all_toc, tenant_id, opportunity_id, "custom", client, 4344, "SYSTEM"
    # )

    # structure_compliance = await generate_structure_compliance(
    #     opportunity_id,
    #     tenant_id,
    #     "custom"
    # )
    # print("="*80)
    # print(type(structure_compliance))
    # print(structure_compliance)

    # content_compliance = await generate_content_compliance(
    #     opportunity_id,
    #     tenant_id,
    #     "custom"
    # )
    # print("="*80)
    # print(type(content_compliance))
    # print(content_compliance)



    # table_of_content = await generate_table_of_contents(
    #     structure_compliance,
    #     content_compliance,
    #     opportunity_id,
    #     "custom"
    # )
    # print("="*80)
    # print(type(table_of_content))
    # print(table_of_content)


    # outlines = await generate_proposal_outline_custom(
    #     opportunity_id,
    #     tenant_id
    # )
    # print("="*80)
    # print(type(outlines))
    # print(outlines)


    # with open("output.pdf", "wb") as f:
    #     f.write(pdf_bytes)


    # req = await generate_submission_requirements(
    #     opportunity_id, "custom"
    # )

    # print(req)


if __name__ == "__main__":
    asyncio.run(main())